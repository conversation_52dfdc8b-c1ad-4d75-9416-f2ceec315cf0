# Google Cloud Firestore Setup Guide for SplitWise App

## What You're Setting Up
- **Google Cloud Firestore**: NoSQL document database for storing app data
- **Firebase Console**: Web interface to manage your Firestore database
- **Firebase Auth**: Authentication service to secure Firestore access

## Quick Setup Checklist

### ✅ **1. Create Google Cloud Firestore Database**
- [ ] Go to [Firebase Console](https://console.firebase.google.com/) (interface for Google Cloud services)
- [ ] Create new project: "splitwise-app" (this creates your Google Cloud project)
- [ ] Go to **Firestore Database** → Create database
- [ ] Choose **Test mode** (for development)
- [ ] Select location closest to your users

### ✅ **2. Configure Authentication (Required for Firestore Access)**
- [ ] Go to Authentication → Sign-in method
- [ ] Enable Email/Password
- [ ] Enable Google (add SHA-1 fingerprint)
- [ ] This creates the authentication layer for your Firestore database

### ✅ **3. Connect Your App to Firestore**
- [ ] Project Settings → Add Android app
- [ ] Package name: `com.example.flutter_try`
- [ ] Download `google-services.json` (contains Firestore connection config)

### ✅ **4. Update App Configuration**
- [ ] Copy config from Project Settings → General
- [ ] Replace values in `lib/firebase_options.dart`
- [ ] Place `google-services.json` in `android/app/` directory

### ✅ **5. Deploy Firestore Security Rules**
```bash
# Install Firebase CLI (connects to Google Cloud)
npm install -g firebase-tools

# Authenticate to Google Cloud
firebase login

# Deploy security rules for Firestore
firebase deploy --only firestore:rules
```

## 🔐 **How Authentication to Google Cloud Firestore Works**

### **Authentication Flow**
1. **User Login**: User signs in via Google/Email in your app
2. **Firebase Auth**: Creates authentication token
3. **Firestore Access**: Token automatically sent with Firestore requests
4. **Security Rules**: Firestore validates token and applies access rules

### **Security Rules (Already Provided)**
The app includes `firestore.rules` that:
- Allow users to read/write their own user document
- Allow group members to access group data
- Deny access to unauthorized users

### **No Additional Authentication Needed**
Once you deploy the rules, authentication is automatic:
- App handles login → token creation → Firestore access
- You don't need to manually authenticate to Firestore
- Security is handled by the rules and Firebase Auth tokens

## 📋 **Google Cloud Firestore Configuration**

### **Your Project Config**
Update `lib/firebase_options.dart` with your actual Google Cloud project values:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSy...', // From Google Cloud Console
  appId: '1:123456789:android:...', // Your Android app ID
  messagingSenderId: '123456789', // Your project number
  projectId: 'your-project-id', // ⭐ This connects to your Firestore database
  storageBucket: 'your-project-id.appspot.com',
);
```

### **Firestore Database Structure**
Your Google Cloud Firestore will contain these collections:

- **`users`**: User profiles and authentication data
- **`groups`**: Bill-splitting groups with member information
- **`expenses`**: Individual expenses within groups

### **Real-time Data Sync**
The app uses Firestore's real-time listeners:
- Changes in database instantly appear in app
- Multiple users see updates immediately
- Offline support with automatic sync when online

## Testing the Setup

1. **Run the app**: `flutter run`
2. **Test Google Sign-In**: Should navigate to dashboard after login
3. **Create a group**: Should persist in Firestore
4. **Check Firestore Console**: Verify data is being saved

## Troubleshooting

### Google Sign-In Issues
- Ensure SHA-1 fingerprint is added to Firebase
- Check package name matches exactly
- Verify `google-services.json` is in correct location

### Firestore Permission Issues
- Check Firestore rules allow authenticated users
- Verify user is properly authenticated
- Check console for specific error messages

### Navigation Issues
- Clear app data and restart
- Check authentication state in debug logs
- Verify router configuration is correct
