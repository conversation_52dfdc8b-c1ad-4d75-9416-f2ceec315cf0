import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_try/models/expense_model.dart';

void main() {
  group('ExpenseModel', () {
    test('should create an ExpenseModel with required fields', () {
      final expense = ExpenseModel(
        id: 'expense-1',
        groupId: 'group-1',
        description: 'Dinner',
        totalAmount: 100.0,
        paidBy: 'user-1',
        paidByName: '<PERSON>',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        splitType: SplitType.equal,
        splits: [],
      );

      expect(expense.id, 'expense-1');
      expect(expense.groupId, 'group-1');
      expect(expense.description, 'Dinner');
      expect(expense.totalAmount, 100.0);
      expect(expense.splitType, SplitType.equal);
    });

    test('should calculate total split amount correctly', () {
      final splits = [
        ExpenseSplit(userId: 'user-1', displayName: 'John', amount: 50.0),
        ExpenseSplit(userId: 'user-2', displayName: 'Jane', amount: 30.0),
        ExpenseSplit(userId: 'user-3', displayName: 'Bob', amount: 20.0),
      ];

      final expense = ExpenseModel(
        id: 'expense-1',
        groupId: 'group-1',
        description: 'Dinner',
        totalAmount: 100.0,
        paidBy: 'user-1',
        paidByName: 'John Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        splitType: SplitType.exact,
        splits: splits,
      );

      expect(expense.getTotalSplitAmount(), 100.0);
    });

    test('should get amount for specific user', () {
      final splits = [
        ExpenseSplit(userId: 'user-1', displayName: 'John', amount: 50.0),
        ExpenseSplit(userId: 'user-2', displayName: 'Jane', amount: 30.0),
        ExpenseSplit(userId: 'user-3', displayName: 'Bob', amount: 20.0),
      ];

      final expense = ExpenseModel(
        id: 'expense-1',
        groupId: 'group-1',
        description: 'Dinner',
        totalAmount: 100.0,
        paidBy: 'user-1',
        paidByName: 'John Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        splitType: SplitType.exact,
        splits: splits,
      );

      expect(expense.getAmountForUser('user-1'), 50.0);
      expect(expense.getAmountForUser('user-2'), 30.0);
      expect(expense.getAmountForUser('user-4'), 0.0); // User not in splits
    });

    test('should check if user is involved in expense', () {
      final splits = [
        ExpenseSplit(userId: 'user-2', displayName: 'Jane', amount: 50.0),
        ExpenseSplit(userId: 'user-3', displayName: 'Bob', amount: 50.0),
      ];

      final expense = ExpenseModel(
        id: 'expense-1',
        groupId: 'group-1',
        description: 'Dinner',
        totalAmount: 100.0,
        paidBy: 'user-1',
        paidByName: 'John Doe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        splitType: SplitType.exact,
        splits: splits,
      );

      expect(expense.isUserInvolved('user-1'), true); // Paid by this user
      expect(expense.isUserInvolved('user-2'), true); // In splits
      expect(expense.isUserInvolved('user-4'), false); // Not involved
    });
  });

  group('ExpenseSplit', () {
    test('should create an ExpenseSplit with required fields', () {
      final split = ExpenseSplit(
        userId: 'user-1',
        displayName: 'John Doe',
        amount: 50.0,
      );

      expect(split.userId, 'user-1');
      expect(split.displayName, 'John Doe');
      expect(split.amount, 50.0);
      expect(split.isPaid, false);
    });

    test('should convert to and from map', () {
      final split = ExpenseSplit(
        userId: 'user-1',
        displayName: 'John Doe',
        amount: 50.0,
        percentage: 50.0,
        isPaid: true,
      );

      final map = split.toMap();
      expect(map['userId'], 'user-1');
      expect(map['displayName'], 'John Doe');
      expect(map['amount'], 50.0);
      expect(map['percentage'], 50.0);
      expect(map['isPaid'], true);

      final splitFromMap = ExpenseSplit.fromMap(map);
      expect(splitFromMap.userId, split.userId);
      expect(splitFromMap.displayName, split.displayName);
      expect(splitFromMap.amount, split.amount);
      expect(splitFromMap.percentage, split.percentage);
      expect(splitFromMap.isPaid, split.isPaid);
    });

    test('should create copy with updated fields', () {
      final split = ExpenseSplit(
        userId: 'user-1',
        displayName: 'John Doe',
        amount: 50.0,
      );

      final updatedSplit = split.copyWith(
        amount: 75.0,
        isPaid: true,
      );

      expect(updatedSplit.userId, 'user-1');
      expect(updatedSplit.displayName, 'John Doe');
      expect(updatedSplit.amount, 75.0);
      expect(updatedSplit.isPaid, true);
    });
  });

  group('SplitType', () {
    test('should have correct enum values', () {
      expect(SplitType.values.length, 3);
      expect(SplitType.values, contains(SplitType.equal));
      expect(SplitType.values, contains(SplitType.percentage));
      expect(SplitType.values, contains(SplitType.exact));
    });
  });
}
