// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:flutter_try/providers/auth_provider.dart';
import 'package:flutter_try/providers/group_provider.dart';
import 'package:flutter_try/providers/expense_provider.dart';

void main() {
  testWidgets('App loads correctly without Firebase', (WidgetTester tester) async {
    // Create a simple test app without Firebase dependencies
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => AuthProvider()),
          ChangeNotifierProvider(create: (_) => GroupProvider()),
          ChangeNotifierProvider(create: (_) => ExpenseProvider()),
        ],
        child: MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Test App')),
            body: const Center(child: Text('Test')),
          ),
        ),
      ),
    );

    // Verify that the app loads
    expect(find.byType(MaterialApp), findsOneWidget);
    expect(find.text('Test App'), findsOneWidget);
  });

  testWidgets('App structure test', (WidgetTester tester) async {
    // Test basic app structure without Firebase
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          appBar: AppBar(title: const Text('SplitWise')),
          body: const Center(child: Text('Welcome to SplitWise')),
        ),
      ),
    );

    expect(find.text('SplitWise'), findsOneWidget);
    expect(find.text('Welcome to SplitWise'), findsOneWidget);
  });
}
