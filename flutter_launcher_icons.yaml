# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  # Main icon path - should be relative to project root
  image_path: "assets/icon/app_icon.jpg"

  # Android configuration
  android: "launcher_icon"
  min_sdk_android: 21

  # For better Android adaptive icons, you can uncomment and customize these:
  # adaptive_icon_background: "assets/icon/background.png"
  # adaptive_icon_foreground: "assets/icon/foreground.png"

  # iOS configuration
  ios: true
  remove_alpha_ios: true

  # Web configuration
  web:
    generate: true
    image_path: "assets/icon/app_icon.jpg"
    background_color: "#2196F3"  # Blue theme for SplitWise
    theme_color: "#2196F3"

  # Windows configuration
  windows:
    generate: true
    image_path: "assets/icon/app_icon.jpg"
    icon_size: 48

  # macOS configuration
  macos:
    generate: true
    image_path: "assets/icon/app_icon.jpg"
