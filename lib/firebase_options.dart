// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDVw37AMHZfpLGa_qDI8HJwWag81g_bu-U', // Use same as Android for now
    appId: '1:1051142728320:web:09d8c623d051694f905465', // Replace with real value from Firebase Console
    messagingSenderId: '1051142728320',
    projectId: 'splitwise-app-3c2b9',
    authDomain: 'splitwise-app-3c2b9.firebaseapp.com',
    storageBucket: 'splitwise-app-3c2b9.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDVw37AMHZfpLGa_qDI8HJwWag81g_bu-U',
    appId: '1:1051142728320:android:c3c0015f91db2131905465',
    messagingSenderId: '123456789',
    projectId: 'splitwise-app-3c2b9',
    storageBucket: 'splitwise-app-3c2b9.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDemoKey123456789',
    appId: '1:123456789:ios:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'splitwise-demo',
    storageBucket: 'splitwise-demo.appspot.com',
    iosBundleId: 'com.example.flutterTry',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDemoKey123456789',
    appId: '1:123456789:macos:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'splitwise-demo',
    storageBucket: 'splitwise-demo.appspot.com',
    iosBundleId: 'com.example.flutterTry',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDemoKey123456789',
    appId: '1:123456789:windows:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'splitwise-demo',
    storageBucket: 'splitwise-demo.appspot.com',
  );
}
