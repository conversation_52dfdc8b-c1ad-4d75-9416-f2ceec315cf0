import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/group_service.dart';
import '../services/expense_service.dart';

class GroupProvider with ChangeNotifier {
  final GroupService _groupService = GroupService();
  final ExpenseService _expenseService = ExpenseService();
  
  List<GroupModel> _groups = [];
  GroupModel? _selectedGroup;
  GroupBalance? _selectedGroupBalance;
  List<ExpenseModel> _selectedGroupExpenses = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<GroupModel> get groups => _groups;
  GroupModel? get selectedGroup => _selectedGroup;
  GroupBalance? get selectedGroupBalance => _selectedGroupBalance;
  List<ExpenseModel> get selectedGroupExpenses => _selectedGroupExpenses;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load user's groups
  void loadUserGroups() {
    _groupService.getUserGroups().listen(
      (groups) {
        _groups = groups;
        _error = null;
        _isLoading = false;
        notifyListeners();
      },
      onError: (error) {
        _error = error.toString();
        _isLoading = false;
        notifyListeners();
      },
    );
  }

  // Select a group and load its details
  Future<void> selectGroup(String groupId) async {
    _setLoading(true);
    try {
      _selectedGroup = await _groupService.getGroup(groupId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Load expenses for a specific group
  Future<void> loadGroupExpenses(String groupId) async {
    try {
      _selectedGroupExpenses = await _expenseService.getGroupExpenses(groupId);
      _error = null;
      notifyListeners();
      print('📊 GroupProvider loaded ${_selectedGroupExpenses.length} expenses for group $groupId');
    } catch (e) {
      _error = e.toString();
      _selectedGroupExpenses = [];
      notifyListeners();
      print('❌ GroupProvider failed to load expenses: $e');
    }
  }

  // Create a new group
  Future<String> createGroup(GroupModel group, List<String> memberEmails) async {
    _setLoading(true);
    try {
      final groupId = await _groupService.createGroup(group, memberEmails);
      _error = null;
      // Reload groups to include the new one
      loadUserGroups();
      return groupId;
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update group
  Future<void> updateGroup(GroupModel group) async {
    _setLoading(true);
    try {
      await _groupService.updateGroup(group);
      _selectedGroup = group;
      _error = null;
      // Reload groups to reflect changes
      loadUserGroups();
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Add member to group
  Future<void> addMemberToGroup(String groupId, String userEmail) async {
    _setLoading(true);
    try {
      await _groupService.addMemberToGroup(groupId, userEmail);
      _error = null;
      // Reload group details
      if (_selectedGroup?.id == groupId) {
        await selectGroup(groupId);
      }
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Remove member from group
  Future<void> removeMemberFromGroup(String groupId, String userId) async {
    _setLoading(true);
    try {
      await _groupService.removeMemberFromGroup(groupId, userId);
      _error = null;
      // Reload group details
      if (_selectedGroup?.id == groupId) {
        await selectGroup(groupId);
      }
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete group
  Future<void> deleteGroup(String groupId) async {
    _setLoading(true);
    try {
      await _groupService.deleteGroup(groupId);
      _error = null;
      // Clear selected group if it was deleted
      if (_selectedGroup?.id == groupId) {
        _selectedGroup = null;
        _selectedGroupBalance = null;
        _selectedGroupExpenses = [];
      }
      // Reload groups
      loadUserGroups();
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Get group by ID
  GroupModel? getGroupById(String groupId) {
    try {
      return _groups.firstWhere((group) => group.id == groupId);
    } catch (e) {
      return null;
    }
  }

  // Clear selected group
  void clearSelectedGroup() {
    _selectedGroup = null;
    _selectedGroupBalance = null;
    _selectedGroupExpenses = [];
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Get user's total statistics
  Map<String, dynamic> getUserStatistics() {
    double totalExpenses = 0.0;
    int totalGroups = _groups.length;
    int totalMembers = 0;

    for (final group in _groups) {
      totalExpenses += group.totalExpenses;
      totalMembers += group.memberCount;
    }

    return {
      'totalGroups': totalGroups,
      'totalExpenses': totalExpenses,
      'totalMembers': totalMembers,
      'averageExpensePerGroup': totalGroups > 0 ? totalExpenses / totalGroups : 0.0,
    };
  }

  // Search groups
  List<GroupModel> searchGroups(String query) {
    if (query.isEmpty) return _groups;
    
    final lowercaseQuery = query.toLowerCase();
    return _groups.where((group) {
      return group.name.toLowerCase().contains(lowercaseQuery) ||
             group.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }


}
