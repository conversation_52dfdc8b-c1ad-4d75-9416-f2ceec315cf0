import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/expense_service.dart';

class ExpenseProvider with ChangeNotifier {
  final ExpenseService _expenseService = ExpenseService();
  
  List<ExpenseModel> _recentExpenses = [];
  ExpenseModel? _selectedExpense;
  Map<String, double> _categoryTotals = {};
  Map<String, double> _monthlyTotals = {};
  Map<String, double> _userDebtSummary = {};
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ExpenseModel> get recentExpenses => _recentExpenses;
  ExpenseModel? get selectedExpense => _selectedExpense;
  Map<String, double> get categoryTotals => _categoryTotals;
  Map<String, double> get monthlyTotals => _monthlyTotals;
  Map<String, double> get userDebtSummary => _userDebtSummary;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load recent expenses
  Future<void> loadRecentExpenses({int limit = 10}) async {
    _setLoading(true);
    try {
      _recentExpenses = await _expenseService.getRecentExpenses(limit: limit);
      _error = null;
    } catch (error) {
      _error = error.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Add a new expense
  Future<String> addExpense(ExpenseModel expense) async {
    _setLoading(true);
    try {
      final expenseId = await _expenseService.addExpense(expense);
      _error = null;
      // Reload recent expenses to include the new one
      await loadRecentExpenses();
      return expenseId;
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update an expense
  Future<void> updateExpense(ExpenseModel expense) async {
    _setLoading(true);
    try {
      await _expenseService.updateExpense(expense);
      _selectedExpense = expense;
      _error = null;
      // Reload recent expenses to reflect changes
      await loadRecentExpenses();
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete an expense
  Future<void> deleteExpense(String expenseId) async {
    _setLoading(true);
    try {
      await _expenseService.deleteExpense(expenseId);
      _error = null;
      // Clear selected expense if it was deleted
      if (_selectedExpense?.id == expenseId) {
        _selectedExpense = null;
      }
      // Reload recent expenses
      await loadRecentExpenses();
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Select an expense
  Future<void> selectExpense(String expenseId) async {
    _setLoading(true);
    try {
      _selectedExpense = await _expenseService.getExpense(expenseId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Load expenses by category for a group
  Future<void> loadExpensesByCategory(String groupId) async {
    _setLoading(true);
    try {
      _categoryTotals = await _expenseService.getExpensesByCategory(groupId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Load monthly expense summary for a group
  Future<void> loadMonthlyExpenseSummary(String groupId, int year) async {
    _setLoading(true);
    try {
      _monthlyTotals = await _expenseService.getMonthlyExpenseSummary(groupId, year);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Search expenses in a group
  Future<List<ExpenseModel>> searchExpenses(String groupId, String query) async {
    try {
      return await _expenseService.searchExpenses(groupId, query);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Mark expense split as paid
  Future<void> markSplitAsPaid(String expenseId, String userId) async {
    _setLoading(true);
    try {
      await _expenseService.markSplitAsPaid(expenseId, userId);
      _error = null;
      // Reload selected expense if it's the one being updated
      if (_selectedExpense?.id == expenseId) {
        await selectExpense(expenseId);
      }
      // Reload recent expenses
      await loadRecentExpenses();
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Load user's debt summary
  Future<void> loadUserDebtSummary(String userId) async {
    _setLoading(true);
    try {
      _userDebtSummary = await _expenseService.getUserDebtSummary(userId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Get expense statistics for a group
  Map<String, dynamic> getGroupExpenseStatistics(List<ExpenseModel> expenses) {
    if (expenses.isEmpty) {
      return {
        'totalExpenses': 0.0,
        'averageExpense': 0.0,
        'expenseCount': 0,
        'mostExpensiveCategory': null,
        'mostActiveUser': null,
      };
    }

    double totalExpenses = 0.0;
    final categoryTotals = <String, double>{};
    final userExpenseCounts = <String, int>{};

    for (final expense in expenses) {
      totalExpenses += expense.totalAmount;
      
      // Track category totals
      final category = expense.category ?? 'Other';
      categoryTotals[category] = (categoryTotals[category] ?? 0.0) + expense.totalAmount;
      
      // Track user activity
      userExpenseCounts[expense.paidBy] = (userExpenseCounts[expense.paidBy] ?? 0) + 1;
    }

    // Find most expensive category
    String? mostExpensiveCategory;
    double maxCategoryAmount = 0.0;
    categoryTotals.forEach((category, amount) {
      if (amount > maxCategoryAmount) {
        maxCategoryAmount = amount;
        mostExpensiveCategory = category;
      }
    });

    // Find most active user
    String? mostActiveUser;
    int maxUserExpenses = 0;
    userExpenseCounts.forEach((userId, count) {
      if (count > maxUserExpenses) {
        maxUserExpenses = count;
        mostActiveUser = userId;
      }
    });

    return {
      'totalExpenses': totalExpenses,
      'averageExpense': totalExpenses / expenses.length,
      'expenseCount': expenses.length,
      'mostExpensiveCategory': mostExpensiveCategory,
      'mostActiveUser': mostActiveUser,
      'categoryBreakdown': categoryTotals,
    };
  }

  // Get expenses for a specific date range
  List<ExpenseModel> getExpensesInDateRange(
    List<ExpenseModel> expenses,
    DateTime startDate,
    DateTime endDate,
  ) {
    return expenses.where((expense) {
      return expense.createdAt.isAfter(startDate) && 
             expense.createdAt.isBefore(endDate);
    }).toList();
  }

  // Get expenses by split type
  Map<SplitType, List<ExpenseModel>> getExpensesBySplitType(List<ExpenseModel> expenses) {
    final result = <SplitType, List<ExpenseModel>>{};
    
    for (final splitType in SplitType.values) {
      result[splitType] = expenses.where((expense) => expense.splitType == splitType).toList();
    }
    
    return result;
  }

  // Clear selected expense
  void clearSelectedExpense() {
    _selectedExpense = null;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }


}
