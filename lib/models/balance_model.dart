class UserBalance {
  final String userId;
  final String displayName;
  final double totalPaid;
  final double totalOwed;
  final double netBalance; // Positive means they are owed money, negative means they owe money

  UserBalance({
    required this.userId,
    required this.displayName,
    required this.totalPaid,
    required this.totalOwed,
    required this.netBalance,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'displayName': displayName,
      'totalPaid': totalPaid,
      'totalOwed': totalOwed,
      'netBalance': netBalance,
    };
  }

  factory UserBalance.fromMap(Map<String, dynamic> map) {
    return UserBalance(
      userId: map['userId'] ?? '',
      displayName: map['displayName'] ?? '',
      totalPaid: (map['totalPaid'] ?? 0.0).toDouble(),
      totalOwed: (map['totalOwed'] ?? 0.0).toDouble(),
      netBalance: (map['netBalance'] ?? 0.0).toDouble(),
    );
  }

  UserBalance copyWith({
    String? userId,
    String? displayName,
    double? totalPaid,
    double? totalOwed,
    double? netBalance,
  }) {
    return UserBalance(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      totalPaid: totalPaid ?? this.totalPaid,
      totalOwed: totalOwed ?? this.totalOwed,
      netBalance: netBalance ?? this.netBalance,
    );
  }

  bool get owesMoneyOverall => netBalance < 0;
  bool get isOwedMoneyOverall => netBalance > 0;
  bool get isSettled => netBalance == 0;

  @override
  String toString() {
    return 'UserBalance(userId: $userId, displayName: $displayName, totalPaid: $totalPaid, totalOwed: $totalOwed, netBalance: $netBalance)';
  }
}

class Settlement {
  final String fromUserId;
  final String fromUserName;
  final String toUserId;
  final String toUserName;
  final double amount;

  Settlement({
    required this.fromUserId,
    required this.fromUserName,
    required this.toUserId,
    required this.toUserName,
    required this.amount,
  });

  Map<String, dynamic> toMap() {
    return {
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'toUserId': toUserId,
      'toUserName': toUserName,
      'amount': amount,
    };
  }

  factory Settlement.fromMap(Map<String, dynamic> map) {
    return Settlement(
      fromUserId: map['fromUserId'] ?? '',
      fromUserName: map['fromUserName'] ?? '',
      toUserId: map['toUserId'] ?? '',
      toUserName: map['toUserName'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
    );
  }

  @override
  String toString() {
    return 'Settlement(from: $fromUserName, to: $toUserName, amount: \$${amount.toStringAsFixed(2)})';
  }
}

class GroupBalance {
  final String groupId;
  final List<UserBalance> userBalances;
  final List<Settlement> suggestedSettlements;
  final double totalGroupExpenses;
  final DateTime lastUpdated;

  GroupBalance({
    required this.groupId,
    required this.userBalances,
    required this.suggestedSettlements,
    required this.totalGroupExpenses,
    required this.lastUpdated,
  });

  Map<String, dynamic> toMap() {
    return {
      'groupId': groupId,
      'userBalances': userBalances.map((balance) => balance.toMap()).toList(),
      'suggestedSettlements': suggestedSettlements.map((settlement) => settlement.toMap()).toList(),
      'totalGroupExpenses': totalGroupExpenses,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory GroupBalance.fromMap(Map<String, dynamic> map) {
    return GroupBalance(
      groupId: map['groupId'] ?? '',
      userBalances: (map['userBalances'] as List<dynamic>?)
              ?.map((balanceMap) => UserBalance.fromMap(balanceMap))
              .toList() ??
          [],
      suggestedSettlements: (map['suggestedSettlements'] as List<dynamic>?)
              ?.map((settlementMap) => Settlement.fromMap(settlementMap))
              .toList() ??
          [],
      totalGroupExpenses: (map['totalGroupExpenses'] ?? 0.0).toDouble(),
      lastUpdated: DateTime.parse(map['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  // Helper methods
  UserBalance? getBalanceForUser(String userId) {
    try {
      return userBalances.firstWhere((balance) => balance.userId == userId);
    } catch (e) {
      return null;
    }
  }

  List<UserBalance> get usersWhoOwe => userBalances.where((balance) => balance.owesMoneyOverall).toList();
  List<UserBalance> get usersWhoAreOwed => userBalances.where((balance) => balance.isOwedMoneyOverall).toList();
  List<UserBalance> get settledUsers => userBalances.where((balance) => balance.isSettled).toList();

  bool get isGroupSettled => userBalances.every((balance) => balance.isSettled);

  @override
  String toString() {
    return 'GroupBalance(groupId: $groupId, totalExpenses: $totalGroupExpenses, userCount: ${userBalances.length}, settlementsNeeded: ${suggestedSettlements.length})';
  }
}
