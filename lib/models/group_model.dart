import 'package:cloud_firestore/cloud_firestore.dart';

class GroupMember {
  final String userId;
  final String displayName;
  final String email;
  final String? photoUrl;
  final DateTime joinedAt;
  final bool isAdmin;

  GroupMember({
    required this.userId,
    required this.displayName,
    required this.email,
    this.photoUrl,
    required this.joinedAt,
    this.isAdmin = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'displayName': displayName,
      'email': email,
      'photoUrl': photoUrl,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'isAdmin': isAdmin,
    };
  }

  factory GroupMember.fromMap(Map<String, dynamic> map) {
    return GroupMember(
      userId: map['userId'] ?? '',
      displayName: map['displayName'] ?? '',
      email: map['email'] ?? '',
      photoUrl: map['photoUrl'],
      joinedAt: (map['joinedAt'] as Timestamp).toDate(),
      isAdmin: map['isAdmin'] ?? false,
    );
  }

  GroupMember copyWith({
    String? userId,
    String? displayName,
    String? email,
    String? photoUrl,
    DateTime? joinedAt,
    bool? isAdmin,
  }) {
    return GroupMember(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      photoUrl: photoUrl ?? this.photoUrl,
      joinedAt: joinedAt ?? this.joinedAt,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }
}

class GroupModel {
  final String id;
  final String name;
  final String description;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<GroupMember> members;
  final double totalExpenses;
  final String? groupImageUrl;

  GroupModel({
    required this.id,
    required this.name,
    this.description = '',
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.members = const [],
    this.totalExpenses = 0.0,
    this.groupImageUrl,
  });

  // Helper to get member IDs for Firestore queries
  List<String> get memberIds => members.map((m) => m.userId).toList();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'members': members.map((member) => member.toMap()).toList(),
      'memberIds': memberIds, // Add for easier querying
      'totalExpenses': totalExpenses,
      'groupImageUrl': groupImageUrl,
    };
  }

  factory GroupModel.fromMap(Map<String, dynamic> map) {
    return GroupModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      createdBy: map['createdBy'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      members: (map['members'] as List<dynamic>?)
              ?.map((memberMap) => GroupMember.fromMap(memberMap))
              .toList() ??
          [],
      totalExpenses: (map['totalExpenses'] ?? 0.0).toDouble(),
      groupImageUrl: map['groupImageUrl'],
    );
  }

  factory GroupModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return GroupModel.fromMap(data);
  }

  GroupModel copyWith({
    String? id,
    String? name,
    String? description,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<GroupMember>? members,
    double? totalExpenses,
    String? groupImageUrl,
  }) {
    return GroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      members: members ?? this.members,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      groupImageUrl: groupImageUrl ?? this.groupImageUrl,
    );
  }

  // Helper methods
  int get memberCount => members.length;

  bool isMember(String userId) {
    return members.any((member) => member.userId == userId);
  }

  bool isAdmin(String userId) {
    return members.any((member) => member.userId == userId && member.isAdmin);
  }

  GroupMember? getMember(String userId) {
    try {
      return members.firstWhere((member) => member.userId == userId);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'GroupModel(id: $id, name: $name, description: $description, createdBy: $createdBy, memberCount: $memberCount, totalExpenses: $totalExpenses)';
  }
}
