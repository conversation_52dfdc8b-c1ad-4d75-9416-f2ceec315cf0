import 'package:cloud_firestore/cloud_firestore.dart';

enum SplitType {
  equal,
  percentage,
  exact,
}

class ExpenseSplit {
  final String userId;
  final String displayName;
  final double amount;
  final double? percentage; // Only used for percentage splits
  final bool isPaid;

  ExpenseSplit({
    required this.userId,
    required this.displayName,
    required this.amount,
    this.percentage,
    this.isPaid = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'displayName': displayName,
      'amount': amount,
      'percentage': percentage,
      'isPaid': isPaid,
    };
  }

  factory ExpenseSplit.fromMap(Map<String, dynamic> map) {
    return ExpenseSplit(
      userId: map['userId'] ?? '',
      displayName: map['displayName'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      percentage: map['percentage']?.toDouble(),
      isPaid: map['isPaid'] ?? false,
    );
  }

  ExpenseSplit copyWith({
    String? userId,
    String? displayName,
    double? amount,
    double? percentage,
    bool? isPaid,
  }) {
    return ExpenseSplit(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      amount: amount ?? this.amount,
      percentage: percentage ?? this.percentage,
      isPaid: isPaid ?? this.isPaid,
    );
  }
}

class ExpenseModel {
  final String id;
  final String groupId;
  final String description;
  final double totalAmount;
  final String paidBy; // User ID who paid
  final String paidByName; // Display name of who paid
  final DateTime createdAt;
  final DateTime updatedAt;
  final SplitType splitType;
  final List<ExpenseSplit> splits;
  final String? category;
  final String? notes;
  final List<String>? receiptUrls;

  ExpenseModel({
    required this.id,
    required this.groupId,
    required this.description,
    required this.totalAmount,
    required this.paidBy,
    required this.paidByName,
    required this.createdAt,
    required this.updatedAt,
    required this.splitType,
    required this.splits,
    this.category,
    this.notes,
    this.receiptUrls,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'groupId': groupId,
      'description': description,
      'totalAmount': totalAmount,
      'paidBy': paidBy,
      'paidByName': paidByName,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'splitType': splitType.toString().split('.').last,
      'splits': splits.map((split) => split.toMap()).toList(),
      'category': category,
      'notes': notes,
      'receiptUrls': receiptUrls,
    };
  }

  factory ExpenseModel.fromMap(Map<String, dynamic> map) {
    return ExpenseModel(
      id: map['id'] ?? '',
      groupId: map['groupId'] ?? '',
      description: map['description'] ?? '',
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      paidBy: map['paidBy'] ?? '',
      paidByName: map['paidByName'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      splitType: _parseSplitType(map['splitType']),
      splits: (map['splits'] as List<dynamic>?)
              ?.map((splitMap) => ExpenseSplit.fromMap(splitMap))
              .toList() ??
          [],
      category: map['category'],
      notes: map['notes'],
      receiptUrls: map['receiptUrls'] != null
          ? List<String>.from(map['receiptUrls'])
          : null,
    );
  }

  factory ExpenseModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return ExpenseModel.fromMap(data);
  }

  static SplitType _parseSplitType(String? splitTypeString) {
    switch (splitTypeString) {
      case 'equal':
        return SplitType.equal;
      case 'percentage':
        return SplitType.percentage;
      case 'exact':
        return SplitType.exact;
      default:
        return SplitType.equal;
    }
  }

  ExpenseModel copyWith({
    String? id,
    String? groupId,
    String? description,
    double? totalAmount,
    String? paidBy,
    String? paidByName,
    DateTime? createdAt,
    DateTime? updatedAt,
    SplitType? splitType,
    List<ExpenseSplit>? splits,
    String? category,
    String? notes,
    List<String>? receiptUrls,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      description: description ?? this.description,
      totalAmount: totalAmount ?? this.totalAmount,
      paidBy: paidBy ?? this.paidBy,
      paidByName: paidByName ?? this.paidByName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      splitType: splitType ?? this.splitType,
      splits: splits ?? this.splits,
      category: category ?? this.category,
      notes: notes ?? this.notes,
      receiptUrls: receiptUrls ?? this.receiptUrls,
    );
  }

  // Helper methods
  double getTotalSplitAmount() {
    return splits.fold(0.0, (total, split) => total + split.amount);
  }

  double getAmountForUser(String userId) {
    final split = splits.where((s) => s.userId == userId).firstOrNull;
    return split?.amount ?? 0.0;
  }

  bool isUserInvolved(String userId) {
    return paidBy == userId || splits.any((split) => split.userId == userId);
  }

  @override
  String toString() {
    return 'ExpenseModel(id: $id, description: $description, totalAmount: $totalAmount, paidBy: $paidByName, splitType: $splitType)';
  }
}
