import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../models/models.dart';
import '../../providers/auth_provider.dart';
import '../../providers/expense_provider.dart';
import '../../providers/group_provider.dart';

class AddExpenseScreen extends StatefulWidget {
  final String groupId;

  const AddExpenseScreen({
    super.key,
    required this.groupId,
  });

  @override
  State<AddExpenseScreen> createState() => _AddExpenseScreenState();
}

class _AddExpenseScreenState extends State<AddExpenseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  SplitType _selectedSplitType = SplitType.equal;
  String? _selectedPaidBy;
  String? _selectedCategory;
  bool _isLoading = false;
  bool _isLoadingGroup = true;

  // Real group members loaded from database
  List<GroupMember> _groupMembers = [];
  List<String> _pendingInvitations = []; // Emails of pending invitations

  final List<String> _categories = [
    'Food & Dining',
    'Transportation',
    'Accommodation',
    'Entertainment',
    'Shopping',
    'Utilities',
    'Healthcare',
    'Other',
  ];

  // Controllers for split amounts/percentages
  final Map<String, TextEditingController> _splitControllers = {};
  final Map<String, bool> _memberIncluded = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadGroupData();
    });
  }

  Future<void> _loadGroupData() async {
    try {
      final groupProvider = Provider.of<GroupProvider>(context, listen: false);
      await groupProvider.selectGroup(widget.groupId);

      final group = groupProvider.selectedGroup;
      if (group != null) {
        setState(() {
          _groupMembers = group.members;
          _isLoadingGroup = false;
        });
        _initializeSplitData();
      }
    } catch (e) {
      setState(() {
        _isLoadingGroup = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading group: $e')),
        );
      }
    }
  }

  void _initializeSplitData() {
    for (final member in _groupMembers) {
      _splitControllers[member.userId] = TextEditingController();
      _memberIncluded[member.userId] = true;
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    for (final controller in _splitControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _calculateEqualSplit() {
    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;
    final includedMembers = _memberIncluded.entries
        .where((entry) => entry.value)
        .length;

    if (includedMembers > 0 && totalAmount > 0) {
      final splitAmount = totalAmount / includedMembers;
      for (final entry in _memberIncluded.entries) {
        if (entry.value) {
          _splitControllers[entry.key]?.text = splitAmount.toStringAsFixed(2);
        } else {
          _splitControllers[entry.key]?.text = '0.00';
        }
      }
    }
  }



  double _getTotalSplitAmount() {
    double total = 0.0;
    for (final entry in _memberIncluded.entries) {
      if (entry.value) {
        final amount = double.tryParse(_splitControllers[entry.key]?.text ?? '0') ?? 0;
        total += amount;
      }
    }
    return total;
  }

  double _getTotalPercentage() {
    double total = 0.0;
    for (final entry in _memberIncluded.entries) {
      if (entry.value) {
        final percentage = double.tryParse(_splitControllers[entry.key]?.text ?? '0') ?? 0;
        total += percentage;
      }
    }
    return total;
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate split amounts
    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;

    if (_selectedSplitType == SplitType.exact) {
      final splitTotal = _getTotalSplitAmount();
      if ((splitTotal - totalAmount).abs() > 0.01) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Split amounts (\$${splitTotal.toStringAsFixed(2)}) must equal total amount (\$${totalAmount.toStringAsFixed(2)})'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    } else if (_selectedSplitType == SplitType.percentage) {
      final totalPercentage = _getTotalPercentage();
      if ((totalPercentage - 100.0).abs() > 0.01) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Percentages must add up to 100% (currently ${totalPercentage.toStringAsFixed(1)}%)'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.userModel;

      if (currentUser == null || _selectedPaidBy == null) {
        throw Exception('User or payer not selected');
      }

      // Create splits
      final splits = <ExpenseSplit>[];
      for (final entry in _memberIncluded.entries) {
        if (entry.value) {
          final member = _groupMembers.firstWhere((m) => m.userId == entry.key);
          double amount = 0.0;

          if (_selectedSplitType == SplitType.equal) {
            amount = totalAmount / _memberIncluded.values.where((included) => included).length;
          } else if (_selectedSplitType == SplitType.percentage) {
            final percentage = double.tryParse(_splitControllers[entry.key]?.text ?? '0') ?? 0;
            amount = (totalAmount * percentage) / 100;
          } else {
            amount = double.tryParse(_splitControllers[entry.key]?.text ?? '0') ?? 0;
          }

          splits.add(ExpenseSplit(
            userId: member.userId,
            displayName: member.displayName,
            amount: amount,
            percentage: _selectedSplitType == SplitType.percentage
                ? double.tryParse(_splitControllers[entry.key]?.text ?? '0')
                : null,
          ));
        }
      }

      final paidByMember = _groupMembers.firstWhere((m) => m.userId == _selectedPaidBy);

      // Create expense
      final expense = ExpenseModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        groupId: widget.groupId,
        description: _descriptionController.text.trim(),
        totalAmount: totalAmount,
        paidBy: _selectedPaidBy!,
        paidByName: paidByMember.displayName,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        splitType: _selectedSplitType,
        splits: splits,
        category: _selectedCategory,
        notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
      );

      // Save expense using the expense provider
      final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
      await expenseProvider.addExpense(expense);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Expense added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding expense: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Expense'),
        actions: [
          TextButton(
            onPressed: (_isLoading || _isLoadingGroup) ? null : _saveExpense,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: _isLoadingGroup
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading group members...'),
                ],
              ),
            )
          : _groupMembers.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.group_off, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No group members found',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Add members to the group first',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBasicInfoSection(),
                        const SizedBox(height: 24),
                        _buildSplitTypeSection(),
                        const SizedBox(height: 24),
                        _buildSplitDetailsSection(),
                        const SizedBox(height: 32),
                        _buildSaveButton(),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Expense Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Description
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description',
            hintText: 'What was this expense for?',
            prefixIcon: Icon(Icons.description),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a description';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Amount
        TextFormField(
          controller: _amountController,
          decoration: const InputDecoration(
            labelText: 'Amount',
            hintText: '0.00',
            prefixIcon: Icon(Icons.attach_money),
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter an amount';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'Please enter a valid amount';
            }
            return null;
          },
          onChanged: (value) {
            if (_selectedSplitType == SplitType.equal) {
              _calculateEqualSplit();
            }
          },
        ),
        const SizedBox(height: 16),

        // Category
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: 'Category (Optional)',
            prefixIcon: Icon(Icons.category),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value;
            });
          },
        ),
        const SizedBox(height: 16),

        // Paid by
        DropdownButtonFormField<String>(
          value: _selectedPaidBy,
          decoration: const InputDecoration(
            labelText: 'Paid by',
            prefixIcon: Icon(Icons.person),
          ),
          items: _groupMembers.map((member) {
            // Check if this member is a real user (not pending invitation)
            final isRealMember = member.userId.isNotEmpty;

            return DropdownMenuItem(
              value: member.userId,
              enabled: isRealMember, // Disable if pending invitation
              child: Row(
                children: [
                  Text(
                    member.displayName,
                    style: TextStyle(
                      color: isRealMember ? null : Colors.grey,
                      fontStyle: isRealMember ? null : FontStyle.italic,
                    ),
                  ),
                  if (!isRealMember) ...[
                    const SizedBox(width: 8),
                    const Text(
                      '(Pending)',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPaidBy = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Please select who paid';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            hintText: 'Additional details...',
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildSplitTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'How to Split',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Split type options
        Column(
          children: [
            RadioListTile<SplitType>(
              title: const Text('Split Equally'),
              subtitle: const Text('Divide the amount equally among selected members'),
              value: SplitType.equal,
              groupValue: _selectedSplitType,
              onChanged: (value) {
                setState(() {
                  _selectedSplitType = value!;
                  _calculateEqualSplit();
                });
              },
            ),
            RadioListTile<SplitType>(
              title: const Text('Split by Percentage'),
              subtitle: const Text('Assign specific percentages to each member'),
              value: SplitType.percentage,
              groupValue: _selectedSplitType,
              onChanged: (value) {
                setState(() {
                  _selectedSplitType = value!;
                  // Clear split controllers for percentage input
                  for (final controller in _splitControllers.values) {
                    controller.clear();
                  }
                });
              },
            ),
            RadioListTile<SplitType>(
              title: const Text('Split by Exact Amount'),
              subtitle: const Text('Specify exactly how much each person owes'),
              value: SplitType.exact,
              groupValue: _selectedSplitType,
              onChanged: (value) {
                setState(() {
                  _selectedSplitType = value!;
                  // Clear split controllers for exact amount input
                  for (final controller in _splitControllers.values) {
                    controller.clear();
                  }
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSplitDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Split Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_selectedSplitType == SplitType.exact)
              Text(
                'Total: \$${_getTotalSplitAmount().toStringAsFixed(2)}',
                style: TextStyle(
                  color: _getTotalSplitAmount() == (double.tryParse(_amountController.text) ?? 0.0)
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (_selectedSplitType == SplitType.percentage)
              Text(
                'Total: ${_getTotalPercentage().toStringAsFixed(1)}%',
                style: TextStyle(
                  color: _getTotalPercentage() == 100.0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),

        // Member split list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _groupMembers.length,
          itemBuilder: (context, index) {
            final member = _groupMembers[index];
            final isIncluded = _memberIncluded[member.userId] ?? false;
            final isRealMember = member.userId.isNotEmpty;

            return Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // Member info
                    Expanded(
                      flex: 2,
                      child: Row(
                        children: [
                          Checkbox(
                            value: isIncluded,
                            onChanged: isRealMember ? (value) {
                              setState(() {
                                _memberIncluded[member.userId] = value ?? false;
                                if (_selectedSplitType == SplitType.equal) {
                                  _calculateEqualSplit();
                                }
                              });
                            } : null, // Disable checkbox for pending invitations
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        member.displayName,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          color: isRealMember ? null : Colors.grey,
                                          fontStyle: isRealMember ? null : FontStyle.italic,
                                        ),
                                      ),
                                    ),
                                    if (!isRealMember)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(4),
                                          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                                        ),
                                        child: const Text(
                                          'Pending',
                                          style: TextStyle(
                                            color: Colors.orange,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                Text(
                                  member.email,
                                  style: TextStyle(
                                    color: isRealMember ? Colors.grey[600] : Colors.grey[400],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Split input
                    Expanded(
                      child: (isIncluded && isRealMember)
                          ? _buildSplitInput(member.userId)
                          : const SizedBox(),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSplitInput(String userId) {
    final controller = _splitControllers[userId]!;

    if (_selectedSplitType == SplitType.equal) {
      return TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: 'Amount',
          prefixText: '\$',
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
        ],
        readOnly: true, // Equal split is calculated automatically
      );
    } else if (_selectedSplitType == SplitType.percentage) {
      return TextFormField(
        controller: controller,
        decoration: const InputDecoration(
          labelText: 'Percentage',
          suffixText: '%',
          isDense: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
        ],
        onChanged: (value) {
          setState(() {
            // Update UI to show total percentage
          });
        },
      );
    } else {
      return TextFormField(
        controller: controller,
        decoration: const InputDecoration(
          labelText: 'Amount',
          prefixText: '\$',
          isDense: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
        ],
        onChanged: (value) {
          setState(() {
            // Update UI to show total amount
          });
        },
      );
    }
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveExpense,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isLoading
            ? const CircularProgressIndicator()
            : const Text('Add Expense'),
      ),
    );
  }
}
