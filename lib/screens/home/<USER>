import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../providers/group_provider.dart';
import '../../services/expense_service.dart';
import '../profile/profile_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;

  // Real expense and balance tracking
  final ExpenseService _expenseService = ExpenseService();
  Map<String, double> _groupTotalExpenses = {};
  double _myTotalExpenses = 0.0;
  double _myNetBalance = 0.0; // Positive = I get back, Negative = I owe
  bool _isLoadingExpenses = false;

  @override
  void initState() {
    super.initState();
    // Load user groups when dashboard initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadGroupsAndExpenses();
    });
  }

  Future<void> _loadGroupsAndExpenses() async {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    groupProvider.loadUserGroups();

    // Wait a bit for groups to load, then load expense data
    await Future.delayed(const Duration(milliseconds: 500));
    await _loadExpenseData();
  }

  Future<void> _loadExpenseData() async {
    setState(() {
      _isLoadingExpenses = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final groupProvider = Provider.of<GroupProvider>(context, listen: false);
      final currentUserId = authProvider.userModel?.id;

      if (currentUserId == null) return;

      double totalExpenses = 0.0;
      double myNetBalance = 0.0;
      Map<String, double> groupTotals = {};

      // Calculate for each group
      for (final group in groupProvider.groups) {
        try {
          final expenses = await _expenseService.getGroupExpenses(group.id);

          // Calculate group total
          final groupTotal = expenses.fold(0.0, (sum, expense) => sum + expense.totalAmount);
          groupTotals[group.id] = groupTotal;
          totalExpenses += groupTotal;

          // Calculate my balance in this group
          for (final expense in expenses) {
            if (expense.paidBy == currentUserId) {
              myNetBalance += expense.totalAmount;
            }

            for (final split in expense.splits) {
              if (split.userId == currentUserId) {
                myNetBalance -= split.amount;
                break;
              }
            }
          }
        } catch (e) {
          print('Error loading expenses for group ${group.id}: $e');
        }
      }

      setState(() {
        _groupTotalExpenses = groupTotals;
        _myTotalExpenses = totalExpenses;
        _myNetBalance = myNetBalance;
        _isLoadingExpenses = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingExpenses = false;
      });
      print('Error loading expense data: $e');
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildDashboardTab(),
          _buildGroupsTab(),
          _buildProfileTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.group),
            label: 'Groups',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex == 1
          ? FloatingActionButton(
              onPressed: () => context.go('/dashboard/create-group'),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildDashboardTab() {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.userModel;

    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 120,
          floating: false,
          pinned: true,
          flexibleSpace: FlexibleSpaceBar(
            title: Text('Welcome, ${user?.displayName ?? 'User'}'),
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.secondary,
                  ],
                ),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildQuickStats(),
              const SizedBox(height: 24),
              _buildRecentActivity(),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats() {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Quick Stats',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Total Groups',
                        '${groupProvider.groups.length}',
                        Icons.group,
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Total Expenses',
                        _isLoadingExpenses
                          ? 'Loading...'
                          : '\$${_myTotalExpenses.toStringAsFixed(2)}',
                        Icons.receipt,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // My Balance Row
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        _myNetBalance >= 0 ? 'You Get Back' : 'You Owe',
                        _isLoadingExpenses
                          ? 'Loading...'
                          : '\$${_myNetBalance.abs().toStringAsFixed(2)}',
                        _myNetBalance >= 0 ? Icons.trending_up : Icons.trending_down,
                        _myNetBalance >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Recent Activity',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                if (groupProvider.groups.isEmpty)
                  Center(
                    child: Column(
                      children: [
                        Icon(Icons.history, size: 48, color: Colors.grey[400]),
                        const SizedBox(height: 8),
                        Text(
                          'No recent activity',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Create a group to start tracking expenses',
                          style: TextStyle(color: Colors.grey[500], fontSize: 12),
                        ),
                      ],
                    ),
                  )
                else
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: groupProvider.groups.length.clamp(0, 3),
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final group = groupProvider.groups[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          child: const Icon(Icons.group, color: Colors.blue),
                        ),
                        title: Text('Group "${group.name}" created'),
                        subtitle: Text(_formatDate(group.createdAt)),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () => context.go('/dashboard/group/${group.id}'),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroupsTab() {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        return CustomScrollView(
          slivers: [
            const SliverAppBar(
              title: Text('My Groups'),
              floating: true,
              snap: true,
            ),
            if (groupProvider.isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (groupProvider.error != null)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading groups',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        groupProvider.error!,
                        style: TextStyle(color: Colors.grey[500]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => groupProvider.loadUserGroups(),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              )
            else if (groupProvider.groups.isEmpty)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.group_add, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'No groups yet',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your first group to start splitting expenses',
                        style: TextStyle(color: Colors.grey[500]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () => context.go('/dashboard/create-group'),
                        icon: const Icon(Icons.add),
                        label: const Text('Create Group'),
                      ),
                    ],
                  ),
                ),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final group = groupProvider.groups[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildGroupCard(group),
                      );
                    },
                    childCount: groupProvider.groups.length,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildGroupCard(GroupModel group) {
    return Card(
      child: InkWell(
        onTap: () => context.go('/dashboard/group/${group.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      group.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              if (group.description.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  group.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.people, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${group.memberCount} members',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _isLoadingExpenses
                      ? 'Loading...'
                      : '\$${(_groupTotalExpenses[group.id] ?? 0.0).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileTab() {
    return const ProfileScreen();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
