import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/models.dart';
import '../../providers/group_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/expense_service.dart';

// Helper class for debt transfers
class DebtTransfer {
  final String fromUserId;
  final String toUserId;
  final double amount;

  DebtTransfer({
    required this.fromUserId,
    required this.toUserId,
    required this.amount,
  });
}

class GroupDetailScreen extends StatefulWidget {
  final String groupId;

  const GroupDetailScreen({
    super.key,
    required this.groupId,
  });

  @override
  State<GroupDetailScreen> createState() => _GroupDetailScreenState();
}

class _GroupDetailScreenState extends State<GroupDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  GroupModel? _group;
  bool _isLoading = true;

  // Expense and balance data
  List<ExpenseModel> _groupExpenses = [];
  Map<String, double> _memberBalances = {};
  double _totalGroupExpenses = 0.0;
  final ExpenseService _expenseService = ExpenseService();

  // Balance display options
  bool _simplifyDebts = false;
  String? _error;



  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadGroup();
    });
  }

  Future<void> _loadGroup() async {
    try {
      final groupProvider = Provider.of<GroupProvider>(context, listen: false);
      await groupProvider.selectGroup(widget.groupId);
      // Load expenses for this group
      await groupProvider.loadGroupExpenses(widget.groupId);

      setState(() {
        _group = groupProvider.selectedGroup;
        _isLoading = false;
        _error = groupProvider.error;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Calculate balance for a specific member
  double _calculateMemberBalance(String userId) {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    final expenses = groupProvider.selectedGroupExpenses;

    double balance = 0.0;

    for (final expense in expenses) {
      // Add amount if this member paid
      if (expense.paidBy == userId) {
        balance += expense.totalAmount;
      }

      // Subtract amount this member owes
      for (final split in expense.splits) {
        if (split.userId == userId) {
          balance -= split.amount;
          break;
        }
      }
    }

    return balance;
  }

  // Calculate total expenses for the group
  double _calculateTotalExpenses() {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    final expenses = groupProvider.selectedGroupExpenses;

    return expenses.fold(0.0, (sum, expense) => sum + expense.totalAmount);
  }

  // Calculate my specific expense amount (what I owe in total)
  double _calculateMyExpense() {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    final expenses = groupProvider.selectedGroupExpenses;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.userModel?.id;

    if (currentUserId == null) return 0.0;

    double myTotalOwed = 0.0;
    for (final expense in expenses) {
      for (final split in expense.splits) {
        if (split.userId == currentUserId) {
          myTotalOwed += split.amount;
          break;
        }
      }
    }

    return myTotalOwed;
  }

  // Calculate simplified debts (A owes B, B owes C -> A owes C)
  List<DebtTransfer> _calculateSimplifiedDebts() {
    if (_group == null) return [];

    // Calculate net balances for each member
    Map<String, double> balances = {};
    for (final member in _group!.members) {
      balances[member.userId] = _calculateMemberBalance(member.userId);
    }

    List<DebtTransfer> transfers = [];

    if (_simplifyDebts) {
      // Simplified algorithm: match creditors with debtors
      var creditors = <String, double>{};
      var debtors = <String, double>{};

      balances.forEach((userId, balance) {
        if (balance > 0.01) {
          creditors[userId] = balance;
        } else if (balance < -0.01) {
          debtors[userId] = -balance;
        }
      });

      // Match debtors with creditors
      for (final debtorEntry in debtors.entries) {
        String debtorId = debtorEntry.key;
        double debtAmount = debtorEntry.value;

        for (final creditorEntry in creditors.entries) {
          String creditorId = creditorEntry.key;
          double creditAmount = creditorEntry.value;

          if (debtAmount <= 0.01 || creditAmount <= 0.01) continue;

          double transferAmount = debtAmount < creditAmount ? debtAmount : creditAmount;

          transfers.add(DebtTransfer(
            fromUserId: debtorId,
            toUserId: creditorId,
            amount: transferAmount,
          ));

          debtAmount -= transferAmount;
          creditors[creditorId] = creditAmount - transferAmount;

          if (debtAmount <= 0.01) break;
        }
      }
    } else {
      // Direct debts: show who owes whom based on individual balances
      balances.forEach((userId, balance) {
        if (balance < -0.01) {
          // This person owes money, find who they should pay
          final member = _group!.members.firstWhere((m) => m.userId == userId);
          transfers.add(DebtTransfer(
            fromUserId: userId,
            toUserId: '', // Will be calculated based on who paid
            amount: -balance,
          ));
        }
      });
    }

    return transfers;
  }

  Widget _buildSettlementInstructions() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUserId = authProvider.userModel?.id;

    if (currentUserId == null || _group == null) {
      return const Center(child: Text('Unable to load settlement instructions'));
    }

    // Calculate who owes whom
    List<Widget> instructions = [];

    for (final member in _group!.members) {
      final balance = _calculateMemberBalance(member.userId);

      if (balance < -0.01) {
        // This person owes money
        final amountOwed = -balance;

        // Find who they should pay (simplified: pay to whoever has positive balance)
        final creditors = _group!.members.where((m) {
          final creditorBalance = _calculateMemberBalance(m.userId);
          return creditorBalance > 0.01;
        }).toList();

        if (creditors.isNotEmpty) {
          final primaryCreditor = creditors.first;
          final isCurrentUserDebtor = member.userId == currentUserId;
          final isCurrentUserCreditor = primaryCreditor.userId == currentUserId;

          Color cardColor = Colors.white;
          if (isCurrentUserDebtor) {
            cardColor = Colors.red.withValues(alpha: 0.1);
          } else if (isCurrentUserCreditor) {
            cardColor = Colors.green.withValues(alpha: 0.1);
          }

          instructions.add(
            Card(
              color: cardColor,
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: isCurrentUserDebtor ? Colors.red : Colors.blue,
                  child: Icon(
                    isCurrentUserDebtor ? Icons.arrow_upward : Icons.arrow_downward,
                    color: Colors.white,
                  ),
                ),
                title: Text(
                  isCurrentUserDebtor
                    ? 'You owe ${primaryCreditor.displayName}'
                    : '${member.displayName} owes ${primaryCreditor.displayName}',
                  style: TextStyle(
                    fontWeight: isCurrentUserDebtor || isCurrentUserCreditor
                      ? FontWeight.bold
                      : FontWeight.normal,
                  ),
                ),
                subtitle: Text(
                  isCurrentUserDebtor
                    ? 'Pay \$${amountOwed.toStringAsFixed(2)} to settle your balance'
                    : isCurrentUserCreditor
                      ? 'You will receive \$${amountOwed.toStringAsFixed(2)}'
                      : 'Transfer: \$${amountOwed.toStringAsFixed(2)}',
                ),
                trailing: Text(
                  '\$${amountOwed.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: isCurrentUserDebtor ? Colors.red : Colors.green,
                  ),
                ),
              ),
            ),
          );
        }
      }
    }

    if (instructions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'All Settled!',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Text('No outstanding balances in this group.'),
          ],
        ),
      );
    }

    return ListView(
      children: instructions,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Loading...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Error loading group',
                style: TextStyle(fontSize: 18, color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadGroup,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_group == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Group Not Found'),
        ),
        body: const Center(
          child: Text('Group not found'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_group!.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _showGroupSettings(context);
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Expenses'),
            Tab(text: 'Balances'),
            Tab(text: 'Members'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildExpensesTab(),
          _buildBalancesTab(),
          _buildMembersTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/dashboard/group/${widget.groupId}/add-expense'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildExpensesTab() {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        final expenses = groupProvider.selectedGroupExpenses;

        if (expenses.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No expenses yet',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Add your first expense to start tracking',
                  style: TextStyle(color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    context.go('/groups/${_group!.id}/add-expense');
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Expense'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: expenses.length,
          itemBuilder: (context, index) {
            final expense = expenses[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.receipt,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                title: Text(expense.description),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Paid by ${expense.paidByName}'),
                    Text(
                      'Split ${expense.splitType.name}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${expense.totalAmount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${expense.createdAt.day}/${expense.createdAt.month}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  _showExpenseDetails(context, expense);
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBalancesTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Group Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Total Expenses:'),
                      Text(
                        '\$${_calculateTotalExpenses().toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('My Expense:'),
                      Text(
                        '\$${_calculateMyExpense().toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Individual Balances',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  const Text('Simplify', style: TextStyle(fontSize: 12)),
                  Switch(
                    value: _simplifyDebts,
                    onChanged: (value) {
                      setState(() {
                        _simplifyDebts = value;
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Member balances list
          ...(_group!.members.map((member) {
            final balance = _calculateMemberBalance(member.userId);
            final authProvider = Provider.of<AuthProvider>(context, listen: false);
            final isCurrentUser = authProvider.userModel?.id == member.userId;

            return Card(
              color: isCurrentUser ? Colors.blue.withValues(alpha: 0.1) : null,
              child: ListTile(
                leading: CircleAvatar(
                  backgroundImage: member.photoUrl != null
                      ? NetworkImage(member.photoUrl!)
                      : null,
                  child: member.photoUrl == null
                      ? Text(member.displayName[0].toUpperCase())
                      : null,
                ),
                title: Row(
                  children: [
                    Text(member.displayName),
                    if (isCurrentUser) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          'You',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                subtitle: Text(member.email),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      balance >= 0 ? 'Gets back' : 'Owes',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '\$${balance.abs().toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: balance >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList()),

          const SizedBox(height: 24),

          // Settlement Instructions
          const Text(
            'Settlement Instructions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),

          Expanded(
            child: _buildSettlementInstructions(),
          ),
        ],
      ),
    );
  }

  Widget _buildMembersTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _group!.members.length,
      itemBuilder: (context, index) {
        final member = _group!.members[index];
        return Card(
          child: ListTile(
            leading: CircleAvatar(
              backgroundImage: member.photoUrl != null
                  ? NetworkImage(member.photoUrl!)
                  : null,
              child: member.photoUrl == null
                  ? Text(member.displayName[0].toUpperCase())
                  : null,
            ),
            title: Text(member.displayName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(member.email),
                Text(
                  'Joined ${member.joinedAt.day}/${member.joinedAt.month}/${member.joinedAt.year}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: member.isAdmin
                ? const Chip(
                    label: Text('Admin'),
                    backgroundColor: Colors.blue,
                    labelStyle: TextStyle(color: Colors.white),
                  )
                : null,
          ),
        );
      },
    );
  }

  void _showExpenseDetails(BuildContext context, ExpenseModel expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(expense.description),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total: \$${expense.totalAmount.toStringAsFixed(2)}'),
            Text('Paid by: ${expense.paidByName}'),
            Text('Split: ${expense.splitType.name}'),
            const SizedBox(height: 16),
            const Text('Split details:'),
            ...expense.splits.map((split) => Padding(
              padding: const EdgeInsets.only(left: 16, top: 4),
              child: Text('${split.displayName}: \$${split.amount.toStringAsFixed(2)}'),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showGroupSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Group'),
              onTap: () {
                Navigator.pop(context);
                _showEditGroupDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('Add Members'),
              onTap: () {
                Navigator.pop(context);
                _showAddMemberDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.exit_to_app),
              title: const Text('Leave Group'),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Group', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteGroupDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  // Edit Group Dialog
  void _showEditGroupDialog() {
    final nameController = TextEditingController(text: _group?.name ?? '');
    final descriptionController = TextEditingController(text: _group?.description ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Group'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Group Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Group name cannot be empty')),
                );
                return;
              }

              try {
                final updatedGroup = _group!.copyWith(
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim(),
                );

                final groupProvider = Provider.of<GroupProvider>(context, listen: false);
                await groupProvider.updateGroup(updatedGroup);

                Navigator.pop(context);
                _loadGroup(); // Reload group data

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Group updated successfully')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error updating group: $e')),
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  // Add Member Dialog
  void _showAddMemberDialog() {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Member'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                border: OutlineInputBorder(),
                hintText: 'Enter member email',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 8),
            Text(
              'An invitation will be shared to this address. You can send it via WhatsApp, Telegram, Email, or any messaging app.',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final email = emailController.text.trim();
              if (email.isEmpty || !email.contains('@')) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a valid email address')),
                );
                return;
              }

              try {
                final groupProvider = Provider.of<GroupProvider>(context, listen: false);
                await groupProvider.addMemberToGroup(_group!.id, email);

                Navigator.pop(context);
                _loadGroup(); // Reload group data

                // Show success with option to manually share
                _showInvitationSentDialog(email);
              } catch (e) {
                if (e.toString().contains('email sending failed')) {
                  // Email failed but invitation was created
                  Navigator.pop(context);
                  _loadGroup();
                  _showManualSharingDialog(email);
                } else if (e.toString().contains('already a member')) {
                  // User is already a member
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('$email is already a member of this group'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error adding member: $e')),
                  );
                }
              }
            },
            child: const Text('Send Invitation'),
          ),
        ],
      ),
    );
  }

  // Leave Group Dialog
  void _showLeaveGroupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Group'),
        content: Text('Are you sure you want to leave "${_group?.name}"? You will no longer have access to this group.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // TODO: Implement leave group functionality
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Leave group feature coming soon!')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error leaving group: $e')),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Leave Group'),
          ),
        ],
      ),
    );
  }

  // Delete Group Dialog
  void _showDeleteGroupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Group'),
        content: Text('Are you sure you want to delete "${_group?.name}"? This action cannot be undone and all expenses will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final groupProvider = Provider.of<GroupProvider>(context, listen: false);
                await groupProvider.deleteGroup(_group!.id);

                Navigator.pop(context); // Close dialog
                Navigator.pop(context); // Go back to groups list

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Group deleted successfully')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error deleting group: $e')),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete Group'),
          ),
        ],
      ),
    );
  }

  // Show invitation sent success dialog
  void _showInvitationSentDialog(String email) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Invitation Shared!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.share, color: Colors.green, size: 48),
            const SizedBox(height: 16),
            Text('Invitation for $email shared successfully!'),
            const SizedBox(height: 8),
            const Text(
              'The sharing dialog opened with your invitation. You can send it via WhatsApp, Telegram, Email, SMS, or any messaging app.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Show manual sharing dialog when sharing fails
  void _showManualSharingDialog(String email) {
    final invitationText = '''
🎉 You're invited to join "${_group?.name}" on SplitWise!

Hi! You've been invited to join this expense group.

${_group?.description != null && _group!.description.isNotEmpty ? '📝 Group Description: ${_group!.description}\n\n' : ''}💰 SplitWise makes it easy to split expenses with friends and family.

📱 To join this group:
1. Download the SplitWise app
2. Sign up with this email: $email
3. You'll automatically be added to "${_group?.name}"

Let's start splitting expenses the easy way! 🚀

---
Sent via SplitWise
''';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Invitation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.share, color: Colors.blue, size: 48),
            const SizedBox(height: 16),
            Text('Share invitation for $email'),
            const SizedBox(height: 16),
            const Text(
              'Copy the invitation text below and share it via any messaging app:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: SelectableText(
                invitationText,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await Share.share(invitationText, subject: 'SplitWise Group Invitation');
                if (mounted) {
                  Navigator.pop(context);
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to share: $e')),
                  );
                }
              }
            },
            child: const Text('Share Again'),
          ),
        ],
      ),
    );
  }
}
