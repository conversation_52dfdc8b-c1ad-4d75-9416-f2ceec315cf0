import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../models/models.dart';
import 'group_service.dart';

/// Authentication service that integrates with Google Cloud Firestore
/// Uses Firebase Auth for authentication and stores user data in Firestore
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    // Web client ID - replace with real value from Firebase Console → Authentication → Google → Web SDK configuration
    clientId: kIsWeb ? '1051142728320-YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com' : null,
  );
  final FirebaseFirestore _firestore = FirebaseFirestore.instance; // Google Cloud Firestore

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(String email, String password) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Register with email and password
  Future<UserCredential?> registerWithEmailAndPassword(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await result.user?.updateDisplayName(displayName);

      // Ensure user document exists
      if (result.user != null) {
        await _ensureUserDocument(result.user!);
      }

      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        // Web-specific Google Sign-In using popup
        final GoogleAuthProvider googleProvider = GoogleAuthProvider();
        googleProvider.addScope('email');
        googleProvider.addScope('profile');

        UserCredential result = await _auth.signInWithPopup(googleProvider);

        // Ensure user document exists (create if new user, or verify existing)
        if (result.user != null) {
          await _ensureUserDocument(result.user!);
        }

        return result;
      } else {
        // Mobile Google Sign-In
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
        if (googleUser == null) return null;

        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        UserCredential result = await _auth.signInWithCredential(credential);

        // Ensure user document exists (create if new user, or verify existing)
        if (result.user != null) {
          await _ensureUserDocument(result.user!);
        }

        return result;
      }
    } catch (e) {
      throw Exception('Google sign-in failed: $e');
    }
  }



  // Sign out
  Future<void> signOut() async {
    try {
      if (!kIsWeb) {
        // Only sign out from GoogleSignIn on mobile platforms
        await _googleSignIn.signOut();
      }
      await _auth.signOut();
    } catch (e) {
      throw Exception('Sign out failed: $e');
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Get user document from Firestore
  Future<UserModel?> getUserDocument(String userId) async {
    try {
      DocumentSnapshot doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return UserModel.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user document: $e');
    }
  }

  // Update user document
  Future<void> updateUserDocument(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).update(user.toMap());
    } catch (e) {
      throw Exception('Failed to update user document: $e');
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(User user, String displayName) async {
    final userModel = UserModel(
      id: user.uid,
      email: user.email ?? '',
      displayName: displayName.isNotEmpty ? displayName : user.displayName ?? '',
      photoUrl: user.photoURL,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      groupIds: [],
    );

    await _firestore.collection('users').doc(user.uid).set(userModel.toMap());
  }

  // Ensure user document exists (create if doesn't exist)
  Future<void> _ensureUserDocument(User user) async {
    final doc = await _firestore.collection('users').doc(user.uid).get();
    final isNewUser = !doc.exists;

    if (isNewUser) {
      await _createUserDocument(user, user.displayName ?? '');

      // Process pending invitations for new users
      if (user.email != null) {
        try {
          final groupService = GroupService();
          await groupService.processPendingInvitations(user.email!);
        } catch (e) {
          print('❌ Error processing pending invitations for ${user.email}: $e');
          // Don't throw error as user creation was successful
        }
      }
    }
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Try again later.';
      case 'operation-not-allowed':
        return 'This sign-in method is not allowed.';
      default:
        return 'An error occurred: ${e.message}';
    }
  }
}
