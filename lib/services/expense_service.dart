import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/models.dart';

class ExpenseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Add a new expense
  Future<String> addExpense(ExpenseModel expense) async {
    try {
      final expenseId = const Uuid().v4();
      final expenseWithId = expense.copyWith(id: expenseId);

      // Save to Firestore
      await _firestore
          .collection('expenses')
          .doc(expenseId)
          .set(expenseWithId.toMap());

      print('✅ Expense saved to Firestore: ${expenseWithId.description} - \$${expenseWithId.totalAmount}');
      return expenseId;
    } catch (e) {
      print('❌ Failed to save expense to Firestore: $e');
      throw Exception('Failed to add expense: $e');
    }
  }

  // Get a specific expense
  Future<ExpenseModel?> getExpense(String expenseId) async {
    try {
      final doc = await _firestore.collection('expenses').doc(expenseId).get();
      if (doc.exists) {
        return ExpenseModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get expense: $e');
    }
  }

  // Update an expense
  Future<void> updateExpense(ExpenseModel expense) async {
    try {
      await _firestore
          .collection('expenses')
          .doc(expense.id)
          .update(expense.toMap());
    } catch (e) {
      throw Exception('Failed to update expense: $e');
    }
  }

  // Delete an expense
  Future<void> deleteExpense(String expenseId) async {
    try {
      await _firestore.collection('expenses').doc(expenseId).delete();
    } catch (e) {
      throw Exception('Failed to delete expense: $e');
    }
  }

  // Get expenses for a specific group
  Future<List<ExpenseModel>> getGroupExpenses(String groupId) async {
    try {
      final querySnapshot = await _firestore
          .collection('expenses')
          .where('groupId', isEqualTo: groupId)
          .orderBy('createdAt', descending: true)
          .get();

      final expenses = querySnapshot.docs
          .map((doc) => ExpenseModel.fromMap(doc.data()))
          .toList();

      print('📊 Loaded ${expenses.length} expenses for group $groupId from Firestore');
      return expenses;
    } catch (e) {
      print('❌ Failed to load group expenses from Firestore: $e');
      throw Exception('Failed to get group expenses: $e');
    }
  }

  // Get recent expenses
  Future<List<ExpenseModel>> getRecentExpenses({int limit = 10}) async {
    try {
      final querySnapshot = await _firestore
          .collection('expenses')
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final expenses = querySnapshot.docs
          .map((doc) => ExpenseModel.fromMap(doc.data()))
          .toList();

      return expenses;
    } catch (e) {
      throw Exception('Failed to get recent expenses: $e');
    }
  }

  // Search expenses
  Future<List<ExpenseModel>> searchExpenses(String groupId, String query) async {
    try {
      // Get all group expenses first
      final groupExpenses = await getGroupExpenses(groupId);

      // Filter by search query
      final filteredExpenses = groupExpenses.where((expense) {
        final description = expense.description.toLowerCase();
        final category = (expense.category ?? '').toLowerCase();
        final paidByName = expense.paidByName.toLowerCase();
        final searchQuery = query.toLowerCase();

        return description.contains(searchQuery) ||
               category.contains(searchQuery) ||
               paidByName.contains(searchQuery);
      }).toList();

      return filteredExpenses;
    } catch (e) {
      throw Exception('Failed to search expenses: $e');
    }
  }

  // Mark expense split as paid
  Future<void> markSplitAsPaid(String expenseId, String userId) async {
    try {
      final expense = await getExpense(expenseId);
      if (expense == null) {
        throw Exception('Expense not found');
      }

      final updatedSplits = expense.splits.map((split) {
        if (split.userId == userId) {
          return split.copyWith(isPaid: true);
        }
        return split;
      }).toList();

      final updatedExpense = expense.copyWith(splits: updatedSplits);
      await updateExpense(updatedExpense);
    } catch (e) {
      throw Exception('Failed to mark split as paid: $e');
    }
  }

  // Get expenses by category for a group
  Future<Map<String, double>> getExpensesByCategory(String groupId) async {
    try {
      final groupExpenses = await getGroupExpenses(groupId);
      final categoryTotals = <String, double>{};

      for (final expense in groupExpenses) {
        final category = expense.category ?? 'Other';
        categoryTotals[category] = (categoryTotals[category] ?? 0.0) + expense.totalAmount;
      }

      return categoryTotals;
    } catch (e) {
      throw Exception('Failed to get expenses by category: $e');
    }
  }

  // Get monthly expense summary for a group
  Future<Map<String, double>> getMonthlyExpenseSummary(String groupId, int year) async {
    try {
      final startOfYear = DateTime(year, 1, 1);
      final endOfYear = DateTime(year + 1, 1, 1);

      final allGroupExpenses = await getGroupExpenses(groupId);
      final groupExpenses = allGroupExpenses.where((e) =>
        e.createdAt.isAfter(startOfYear) &&
        e.createdAt.isBefore(endOfYear)
      );

      final monthlyTotals = <String, double>{};

      // Initialize all months
      for (int month = 1; month <= 12; month++) {
        final monthName = _getMonthName(month);
        monthlyTotals[monthName] = 0.0;
      }

      for (final expense in groupExpenses) {
        final monthName = _getMonthName(expense.createdAt.month);
        monthlyTotals[monthName] = (monthlyTotals[monthName] ?? 0.0) + expense.totalAmount;
      }

      return monthlyTotals;
    } catch (e) {
      throw Exception('Failed to get monthly expense summary: $e');
    }
  }

  // Get user's debt summary across all groups
  Future<Map<String, double>> getUserDebtSummary(String userId) async {
    try {
      // Get all expenses from Firestore
      final querySnapshot = await _firestore
          .collection('expenses')
          .get();

      final allExpenses = querySnapshot.docs
          .map((doc) => ExpenseModel.fromMap(doc.data()))
          .toList();

      final userExpenses = allExpenses.where((expense) => expense.isUserInvolved(userId));

      double totalPaid = 0.0;
      double totalOwed = 0.0;

      for (final expense in userExpenses) {
        if (expense.paidBy == userId) {
          totalPaid += expense.totalAmount;
        }
        
        final userSplit = expense.splits.where((split) => split.userId == userId).firstOrNull;
        if (userSplit != null) {
          totalOwed += userSplit.amount;
        }
      }

      return {
        'totalPaid': totalPaid,
        'totalOwed': totalOwed,
        'netBalance': totalPaid - totalOwed,
      };
    } catch (e) {
      throw Exception('Failed to get user debt summary: $e');
    }
  }

  String _getMonthName(int month) {
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return monthNames[month - 1];
  }
}
