import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/models.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'splitwise.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _usersTable = 'users';
  static const String _groupsTable = 'groups';
  static const String _groupMembersTable = 'group_members';
  static const String _expensesTable = 'expenses';
  static const String _expenseSplitsTable = 'expense_splits';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE $_usersTable (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        displayName TEXT NOT NULL,
        photoUrl TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create groups table
    await db.execute('''
      CREATE TABLE $_groupsTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        createdBy TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        totalExpenses REAL DEFAULT 0.0,
        groupImageUrl TEXT
      )
    ''');

    // Create group members table
    await db.execute('''
      CREATE TABLE $_groupMembersTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        groupId TEXT NOT NULL,
        userId TEXT NOT NULL,
        displayName TEXT NOT NULL,
        email TEXT NOT NULL,
        photoUrl TEXT,
        joinedAt INTEGER NOT NULL,
        isAdmin INTEGER DEFAULT 0,
        FOREIGN KEY (groupId) REFERENCES $_groupsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (userId) REFERENCES $_usersTable (id) ON DELETE CASCADE,
        UNIQUE(groupId, userId)
      )
    ''');

    // Create expenses table
    await db.execute('''
      CREATE TABLE $_expensesTable (
        id TEXT PRIMARY KEY,
        groupId TEXT NOT NULL,
        description TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        paidBy TEXT NOT NULL,
        paidByName TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        splitType TEXT NOT NULL,
        category TEXT,
        notes TEXT,
        FOREIGN KEY (groupId) REFERENCES $_groupsTable (id) ON DELETE CASCADE,
        FOREIGN KEY (paidBy) REFERENCES $_usersTable (id)
      )
    ''');

    // Create expense splits table
    await db.execute('''
      CREATE TABLE $_expenseSplitsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        expenseId TEXT NOT NULL,
        userId TEXT NOT NULL,
        displayName TEXT NOT NULL,
        amount REAL NOT NULL,
        percentage REAL,
        isPaid INTEGER DEFAULT 0,
        FOREIGN KEY (expenseId) REFERENCES $_expensesTable (id) ON DELETE CASCADE,
        FOREIGN KEY (userId) REFERENCES $_usersTable (id)
      )
    ''');
  }

  // User operations
  static Future<void> insertUser(UserModel user) async {
    final db = await database;
    await db.insert(
      _usersTable,
      {
        'id': user.id,
        'email': user.email,
        'displayName': user.displayName,
        'photoUrl': user.photoUrl,
        'createdAt': user.createdAt.millisecondsSinceEpoch,
        'updatedAt': user.updatedAt.millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<UserModel?> getUserById(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _usersTable,
      where: 'id = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return UserModel(
        id: map['id'],
        email: map['email'],
        displayName: map['displayName'],
        photoUrl: map['photoUrl'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
        updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      );
    }
    return null;
  }

  static Future<UserModel?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _usersTable,
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return UserModel(
        id: map['id'],
        email: map['email'],
        displayName: map['displayName'],
        photoUrl: map['photoUrl'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
        updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      );
    }
    return null;
  }

  static Future<void> updateUser(UserModel user) async {
    final db = await database;
    await db.update(
      _usersTable,
      {
        'email': user.email,
        'displayName': user.displayName,
        'photoUrl': user.photoUrl,
        'updatedAt': user.updatedAt.millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Group operations
  static Future<void> insertGroup(GroupModel group) async {
    final db = await database;
    
    await db.transaction((txn) async {
      // Insert group
      await txn.insert(
        _groupsTable,
        {
          'id': group.id,
          'name': group.name,
          'description': group.description,
          'createdBy': group.createdBy,
          'createdAt': group.createdAt.millisecondsSinceEpoch,
          'updatedAt': group.updatedAt.millisecondsSinceEpoch,
          'totalExpenses': group.totalExpenses,
          'groupImageUrl': group.groupImageUrl,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Insert group members
      for (final member in group.members) {
        await txn.insert(
          _groupMembersTable,
          {
            'groupId': group.id,
            'userId': member.userId,
            'displayName': member.displayName,
            'email': member.email,
            'photoUrl': member.photoUrl,
            'joinedAt': member.joinedAt.millisecondsSinceEpoch,
            'isAdmin': member.isAdmin ? 1 : 0,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  static Future<List<GroupModel>> getUserGroups(String userId) async {
    final db = await database;
    
    // Get groups where user is a member
    final List<Map<String, dynamic>> groupMaps = await db.rawQuery('''
      SELECT DISTINCT g.* FROM $_groupsTable g
      INNER JOIN $_groupMembersTable gm ON g.id = gm.groupId
      WHERE gm.userId = ?
      ORDER BY g.updatedAt DESC
    ''', [userId]);

    List<GroupModel> groups = [];
    
    for (final groupMap in groupMaps) {
      // Get members for this group
      final List<Map<String, dynamic>> memberMaps = await db.query(
        _groupMembersTable,
        where: 'groupId = ?',
        whereArgs: [groupMap['id']],
      );

      List<GroupMember> members = memberMaps.map((memberMap) {
        return GroupMember(
          userId: memberMap['userId'],
          displayName: memberMap['displayName'],
          email: memberMap['email'],
          photoUrl: memberMap['photoUrl'],
          joinedAt: DateTime.fromMillisecondsSinceEpoch(memberMap['joinedAt']),
          isAdmin: memberMap['isAdmin'] == 1,
        );
      }).toList();

      groups.add(GroupModel(
        id: groupMap['id'],
        name: groupMap['name'],
        description: groupMap['description'] ?? '',
        createdBy: groupMap['createdBy'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(groupMap['createdAt']),
        updatedAt: DateTime.fromMillisecondsSinceEpoch(groupMap['updatedAt']),
        members: members,
        totalExpenses: groupMap['totalExpenses'] ?? 0.0,
        groupImageUrl: groupMap['groupImageUrl'],
      ));
    }

    return groups;
  }

  static Future<GroupModel?> getGroupById(String groupId) async {
    final db = await database;
    
    final List<Map<String, dynamic>> groupMaps = await db.query(
      _groupsTable,
      where: 'id = ?',
      whereArgs: [groupId],
    );

    if (groupMaps.isEmpty) return null;

    final groupMap = groupMaps.first;
    
    // Get members for this group
    final List<Map<String, dynamic>> memberMaps = await db.query(
      _groupMembersTable,
      where: 'groupId = ?',
      whereArgs: [groupId],
    );

    List<GroupMember> members = memberMaps.map((memberMap) {
      return GroupMember(
        userId: memberMap['userId'],
        displayName: memberMap['displayName'],
        email: memberMap['email'],
        photoUrl: memberMap['photoUrl'],
        joinedAt: DateTime.fromMillisecondsSinceEpoch(memberMap['joinedAt']),
        isAdmin: memberMap['isAdmin'] == 1,
      );
    }).toList();

    return GroupModel(
      id: groupMap['id'],
      name: groupMap['name'],
      description: groupMap['description'] ?? '',
      createdBy: groupMap['createdBy'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(groupMap['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(groupMap['updatedAt']),
      members: members,
      totalExpenses: groupMap['totalExpenses'] ?? 0.0,
      groupImageUrl: groupMap['groupImageUrl'],
    );
  }

  // Close database
  static Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
