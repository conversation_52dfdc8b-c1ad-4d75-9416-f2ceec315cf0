import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../models/models.dart';

class EmailService {
  /// Send group invitation via native sharing (WhatsApp, Telegram, Email, SMS, etc.)
  static Future<void> sendGroupInvitation({
    required String recipientEmail,
    required GroupModel group,
    required String inviterName,
  }) async {
    final subject = 'You\'re invited to join "${group.name}" on SplitWise!';

    final invitationText = '''
🎉 You're invited to join "${group.name}" on SplitWise!

Hi! $inviterName has invited you to join their expense group.

${group.description.isNotEmpty ? '📝 Group Description: ${group.description}\n\n' : ''}💰 SplitWise makes it easy to split expenses with friends and family. Track shared costs, settle debts, and keep everyone happy!

📱 To join this group:
1. Download the SplitWise app
2. Sign up with this email: $recipientEmail
3. You'll automatically be added to "${group.name}"

Let's start splitting expenses the easy way! 🚀

---
Sent via SplitWise
''';

    try {
      // Use native sharing - opens sharing dialog with all available apps
      await Share.share(
        invitationText,
        subject: subject,
      );

      print('✅ Invitation shared successfully via native sharing');
    } catch (e) {
      print('❌ Failed to share invitation: $e');
      throw Exception('Failed to share invitation: $e');
    }
  }

  /// Send expense notification via email
  static Future<void> sendExpenseNotification({
    required String recipientEmail,
    required ExpenseModel expense,
    required GroupModel group,
    required String paidByName,
  }) async {
    final subject = 'New expense added in "${group.name}"';
    final body = _buildExpenseNotificationBody(
      expense: expense,
      groupName: group.name,
      paidByName: paidByName,
    );

    // Try email first, fallback to sharing
    try {
      await _sendEmail(
        to: recipientEmail,
        subject: subject,
        body: body,
      );
    } catch (e) {
      // If email fails, use sharing instead
      await _shareInvitation(
        recipientEmail: recipientEmail,
        subject: subject,
        body: body,
      );
    }
  }

  /// Share invitation using device sharing (fallback when email fails)
  static Future<void> _shareInvitation({
    required String recipientEmail,
    required String subject,
    required String body,
  }) async {
    try {
      print('🔍 Email failed, using sharing instead for: $recipientEmail');

      final shareText = '''
$subject

$body

---
Please share this invitation with: $recipientEmail
''';

      await Share.share(
        shareText,
        subject: subject,
      );

      print('✅ Sharing dialog opened successfully');
    } catch (e) {
      print('❌ Sharing also failed: $e');
      throw Exception('Both email and sharing failed: $e');
    }
  }

  /// Generic email sending method using url_launcher
  static Future<void> _sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: to,
      query: _encodeQueryParameters({
        'subject': subject,
        'body': body,
      }),
    );

    print('🔍 Attempting to send email to: $to');
    print('🔍 Email URI: $emailUri');

    try {
      final canLaunch = await canLaunchUrl(emailUri);
      print('🔍 Can launch email URI: $canLaunch');

      if (canLaunch) {
        final launched = await launchUrl(
          emailUri,
          mode: LaunchMode.externalApplication, // Force external email app
        );
        print('🔍 Email client launched: $launched');

        if (!launched) {
          throw Exception('Email client failed to launch');
        }
      } else {
        throw Exception('No email client available on this device');
      }
    } catch (e) {
      print('❌ Email sending error: $e');
      throw Exception('Failed to send email: $e');
    }
  }

  /// Build invitation email body
  static String _buildInvitationEmailBody({
    required String groupName,
    required String groupDescription,
    required String inviterName,
    required String recipientEmail,
  }) {
    return '''
Hi there!

$inviterName has invited you to join the group "$groupName" on SplitWise.

${groupDescription.isNotEmpty ? 'Group Description: $groupDescription\n\n' : ''}SplitWise makes it easy to split expenses with friends and family. You can track who owes what and settle up when convenient.

To join this group:
1. Download the SplitWise app
2. Sign up with this email address: $recipientEmail
3. You'll automatically be added to the "$groupName" group

Start splitting expenses the easy way!

Best regards,
The SplitWise Team

---
This invitation was sent by $inviterName. If you don't know this person, you can safely ignore this email.
''';
  }

  /// Build expense notification email body
  static String _buildExpenseNotificationBody({
    required ExpenseModel expense,
    required String groupName,
    required String paidByName,
  }) {
    return '''
Hi!

A new expense has been added to your group "$groupName":

Expense: ${expense.description}
Amount: \$${expense.totalAmount.toStringAsFixed(2)}
Paid by: $paidByName
Date: ${expense.createdAt.toString().split(' ')[0]}

${expense.notes?.isNotEmpty == true ? 'Notes: ${expense.notes}\n\n' : ''}Open the SplitWise app to see the details and your share of this expense.

Best regards,
The SplitWise Team
''';
  }

  /// Encode query parameters for URL
  static String _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  /// Send settlement reminder
  static Future<void> sendSettlementReminder({
    required String recipientEmail,
    required String recipientName,
    required String groupName,
    required double amount,
    required String creditorName,
  }) async {
    final subject = 'Settlement reminder for "$groupName"';
    final body = '''
Hi $recipientName,

This is a friendly reminder about your balance in the group "$groupName".

You owe \$${amount.toStringAsFixed(2)} to $creditorName.

Open the SplitWise app to settle up or view the details.

Best regards,
The SplitWise Team
''';

    await _sendEmail(
      to: recipientEmail,
      subject: subject,
      body: body,
    );
  }
}
