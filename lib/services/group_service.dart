import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/models.dart';
import 'email_service.dart';

/// Service for managing groups in Google Cloud Firestore
/// Firebase is the interface to Google Cloud Firestore database
class GroupService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance; // Google Cloud Firestore instance
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user's groups
  Stream<List<GroupModel>> getUserGroups() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return Stream.value([]);

    return _firestore
        .collection('groups')
        .where('memberIds', arrayContains: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) => GroupModel.fromSnapshot(doc)).toList();
    });
  }

  // Get a specific group
  Future<GroupModel?> getGroup(String groupId) async {
    try {
      final doc = await _firestore.collection('groups').doc(groupId).get();
      if (doc.exists) {
        return GroupModel.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get group: $e');
    }
  }

  // Create a new group
  Future<String> createGroup(GroupModel group, List<String> memberEmails) async {
    try {
      final docRef = _firestore.collection('groups').doc();
      final groupWithId = group.copyWith(id: docRef.id);

      await docRef.set(groupWithId.toMap());

      // Update user's group list
      final userId = _auth.currentUser?.uid;
      if (userId != null) {
        await _addGroupToUser(userId, docRef.id);
      }

      // Send email invitations to members
      if (memberEmails.isNotEmpty) {
        await _sendGroupInvitations(groupWithId, memberEmails);
      }

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create group: $e');
    }
  }

  // Update group
  Future<void> updateGroup(GroupModel group) async {
    try {
      await _firestore.collection('groups').doc(group.id).update(group.toMap());
    } catch (e) {
      throw Exception('Failed to update group: $e');
    }
  }

  // Add member to group (supports both existing users and email invitations)
  Future<void> addMemberToGroup(String groupId, String userEmail) async {
    try {
      // Get the group first
      final groupDoc = await _firestore.collection('groups').doc(groupId).get();
      if (!groupDoc.exists) {
        throw Exception('Group not found');
      }

      final group = GroupModel.fromSnapshot(groupDoc);

      // Check if email is already a member or has pending invitation
      final isAlreadyMember = group.members.any((member) => member.email.toLowerCase() == userEmail.toLowerCase());
      if (isAlreadyMember) {
        throw Exception('User is already a member of this group');
      }

      // Check for existing pending invitation
      final existingInvitation = await _firestore
          .collection('group_invitations')
          .where('groupId', isEqualTo: groupId)
          .where('email', isEqualTo: userEmail.toLowerCase())
          .where('status', isEqualTo: 'pending')
          .get();

      bool isResendingInvitation = false;
      String? existingInvitationId;

      if (existingInvitation.docs.isNotEmpty) {
        // Allow re-sending invitation - update existing invitation timestamp
        isResendingInvitation = true;
        existingInvitationId = existingInvitation.docs.first.id;
        print('📧 Re-sending invitation to existing pending invite: $userEmail');
      }

      // Try to find existing user
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail.toLowerCase())
          .get();

      if (userQuery.docs.isNotEmpty) {
        // User exists - add them directly to the group
        final userDoc = userQuery.docs.first;
        final userData = UserModel.fromSnapshot(userDoc);

        final newMember = GroupMember(
          userId: userData.id,
          displayName: userData.displayName,
          email: userData.email,
          photoUrl: userData.photoUrl,
          joinedAt: DateTime.now(),
          isAdmin: false,
        );

        final updatedMembers = [...group.members, newMember];
        await _firestore.collection('groups').doc(groupId).update({
          'members': updatedMembers.map((m) => m.toMap()).toList(),
          'memberIds': updatedMembers.map((m) => m.userId).toList(),
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });

        // Add group to user's group list
        await _addGroupToUser(userData.id, groupId);
      } else {
        // User doesn't exist - create or update pending invitation
        await _createOrUpdatePendingInvitation(
          groupId,
          userEmail,
          group,
          existingInvitationId: existingInvitationId,
        );
      }

      // Send email invitation regardless of whether user exists
      try {
        await _sendGroupInvitations(group, [userEmail]);
      } catch (e) {
        // If email fails, still consider the invitation successful but inform user
        throw Exception('Member invitation created successfully, but email sending failed: $e. Please manually share the group details with the member.');
      }

      // Send email invitation to the new member
      try {
        final currentUser = _auth.currentUser;
        if (currentUser != null) {
          final inviterName = currentUser.displayName ?? currentUser.email ?? 'Someone';
          await EmailService.sendGroupInvitation(
            recipientEmail: userEmail,
            group: group,
            inviterName: inviterName,
          );
        }
      } catch (e) {
        // Don't fail the member addition if email fails
        print('❌ Failed to send invitation email to $userEmail: $e');
      }
    } catch (e) {
      throw Exception('Failed to add member to group: $e');
    }
  }

  // Remove member from group
  Future<void> removeMemberFromGroup(String groupId, String userId) async {
    try {
      final groupDoc = await _firestore.collection('groups').doc(groupId).get();
      if (!groupDoc.exists) {
        throw Exception('Group not found');
      }

      final group = GroupModel.fromSnapshot(groupDoc);
      final updatedMembers = group.members.where((m) => m.userId != userId).toList();

      await _firestore.collection('groups').doc(groupId).update({
        'members': updatedMembers.map((m) => m.toMap()).toList(),
        'memberIds': updatedMembers.map((m) => m.userId).toList(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Remove group from user's group list
      await _removeGroupFromUser(userId, groupId);
    } catch (e) {
      throw Exception('Failed to remove member from group: $e');
    }
  }

  // Delete group
  Future<void> deleteGroup(String groupId) async {
    try {
      // Get group to find all members
      final groupDoc = await _firestore.collection('groups').doc(groupId).get();
      if (!groupDoc.exists) {
        throw Exception('Group not found');
      }

      final group = GroupModel.fromSnapshot(groupDoc);

      // Remove group from all members' group lists
      for (final member in group.members) {
        await _removeGroupFromUser(member.userId, groupId);
      }

      // Delete the group
      await _firestore.collection('groups').doc(groupId).delete();
    } catch (e) {
      throw Exception('Failed to delete group: $e');
    }
  }

  // Private helper methods
  Future<void> _addGroupToUser(String userId, String groupId) async {
    await _firestore.collection('users').doc(userId).update({
      'groupIds': FieldValue.arrayUnion([groupId]),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    });
  }

  Future<void> _removeGroupFromUser(String userId, String groupId) async {
    await _firestore.collection('users').doc(userId).update({
      'groupIds': FieldValue.arrayRemove([groupId]),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    });
  }

  // Send email invitations to group members
  Future<void> _sendGroupInvitations(GroupModel group, List<String> memberEmails) async {
    try {
      // Get current user info for the invitation
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final inviterName = currentUser.displayName ?? currentUser.email ?? 'Someone';

      // Send invitation to each email
      for (final email in memberEmails) {
        try {
          await EmailService.sendGroupInvitation(
            recipientEmail: email,
            group: group,
            inviterName: inviterName,
          );
          print('📧 Invitation sent to: $email');
        } catch (e) {
          print('❌ Failed to send invitation to $email: $e');
          // Continue with other invitations even if one fails
        }
      }
    } catch (e) {
      print('❌ Error sending group invitations: $e');
      // Don't throw error here as group creation was successful
    }
  }

  // Create or update pending invitation for users who haven't signed up yet
  Future<void> _createOrUpdatePendingInvitation(
    String groupId,
    String email,
    GroupModel group, {
    String? existingInvitationId,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final invitationData = {
        'groupId': groupId,
        'groupName': group.name,
        'email': email.toLowerCase(),
        'invitedBy': currentUser.uid,
        'invitedByName': currentUser.displayName ?? currentUser.email ?? 'Someone',
        'status': 'pending', // pending, accepted, declined
        'createdAt': Timestamp.fromDate(DateTime.now()), // Always update timestamp for re-sends
        'expiresAt': Timestamp.fromDate(DateTime.now().add(const Duration(days: 30))), // 30 days expiry
      };

      if (existingInvitationId != null) {
        // Update existing invitation (re-send case)
        await _firestore
            .collection('group_invitations')
            .doc(existingInvitationId)
            .update(invitationData);
        print('📧 Updated existing invitation for: $email');
      } else {
        // Create new invitation
        invitationData['id'] = _firestore.collection('group_invitations').doc().id;
        await _firestore.collection('group_invitations').add(invitationData);
        print('📧 Created new invitation for: $email');
      }
    } catch (e) {
      print('❌ Error creating/updating pending invitation: $e');
      // Don't throw error as this is not critical
    }
  }

  // Process pending invitations when user signs up
  Future<void> processPendingInvitations(String userEmail) async {
    try {
      final pendingInvitations = await _firestore
          .collection('group_invitations')
          .where('email', isEqualTo: userEmail.toLowerCase())
          .where('status', isEqualTo: 'pending')
          .get();

      for (final invitationDoc in pendingInvitations.docs) {
        try {
          final invitation = invitationDoc.data();
          final groupId = invitation['groupId'] as String;

          // Get current user
          final currentUser = _auth.currentUser;
          if (currentUser == null) continue;

          // Get the group
          final groupDoc = await _firestore.collection('groups').doc(groupId).get();
          if (!groupDoc.exists) continue;

          final group = GroupModel.fromSnapshot(groupDoc);

          // Add user to group
          final newMember = GroupMember(
            userId: currentUser.uid,
            displayName: currentUser.displayName ?? userEmail.split('@')[0],
            email: userEmail,
            photoUrl: currentUser.photoURL,
            joinedAt: DateTime.now(),
            isAdmin: false,
          );

          final updatedMembers = [...group.members, newMember];
          await _firestore.collection('groups').doc(groupId).update({
            'members': updatedMembers.map((m) => m.toMap()).toList(),
            'memberIds': updatedMembers.map((m) => m.userId).toList(),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });

          // Add group to user's group list
          await _addGroupToUser(currentUser.uid, groupId);

          // Mark invitation as accepted
          await invitationDoc.reference.update({
            'status': 'accepted',
            'acceptedAt': Timestamp.fromDate(DateTime.now()),
          });

          print('✅ Processed pending invitation for group: ${group.name}');
        } catch (e) {
          print('❌ Error processing invitation: $e');
          // Continue with other invitations
        }
      }
    } catch (e) {
      print('❌ Error processing pending invitations: $e');
    }
  }
}
