<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Split expenses with friends and family. Track shared costs, settle debts, and keep everyone happy!">
  <meta name="keywords" content="expense tracker, split bills, shared expenses, group expenses, bill splitting">
  <meta name="author" content="SplitWise">

  <!-- PWA meta tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="SplitWise">
  <meta name="theme-color" content="#2196F3">
  <meta name="msapplication-TileColor" content="#2196F3">

  <!-- Icons -->
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="192x192" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="512x512" href="icons/Icon-512.png">
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" sizes="512x512" href="icons/Icon-512.png">
  <link rel="shortcut icon" href="favicon.png"/>

  <title>SplitWise - Expense Tracker</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
