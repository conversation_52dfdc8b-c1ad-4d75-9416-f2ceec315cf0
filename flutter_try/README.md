# flutter_try

A new Flutter project.

## Getting Started

# SplitWise - Bill Splitting App

A comprehensive Flutter application for splitting expenses with friends and family. Built with Firebase for authentication and data persistence.

## Features

### 🔐 User Authentication
- **Email/Password Authentication**: Secure login and registration system
- **Social Login**: One-click sign-in with Google and Facebook accounts
- **Password Reset**: Forgot password functionality with email recovery

### 📊 Dashboard Overview
- **Central Dashboard**: View all your bill-splitting groups at a glance
- **Group Cards**: Each group displays name, member count, and total expenses
- **Quick Statistics**: Overview of your total groups and expenses
- **Recent Activity**: Track recent expense additions and updates

### 👥 Group Management
- **Create Groups**: Easy group creation with custom names and descriptions
- **Invite Members**: Add friends by email with automatic user creation
- **Group Settings**: Edit group details, add/remove members, and manage permissions
- **Admin Controls**: Group creators have admin privileges for management

### 💰 Flexible Expense Splitting
- **Multiple Split Types**:
  - **Equal Split**: Divide costs evenly among all members
  - **Percentage Split**: Assign specific percentages to each member
  - **Exact Amount**: Specify exactly how much each person owes
- **Expense Categories**: Organize expenses by type (Food, Transportation, etc.)
- **Receipt Notes**: Add additional details and notes to expenses

### 📈 Detailed Group View
- **Transaction History**: Complete breakdown of all group expenses
- **Balance Summary**: Real-time calculation of who owes what
- **Settlement Suggestions**: Smart recommendations for settling debts
- **Member Overview**: View all group members and their participation

### 🎨 Modern UI/UX
- **Material Design 3**: Clean, modern interface following Google's design guidelines
- **Responsive Layout**: Works seamlessly across different screen sizes
- **Dark/Light Theme**: Automatic theme adaptation
- **Intuitive Navigation**: Easy-to-use bottom navigation and routing

## Technical Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **State Management**: Provider pattern
- **Navigation**: GoRouter for declarative routing
- **Authentication**: Firebase Auth with social login support
- **Database**: Cloud Firestore for real-time data synchronization

## Architecture

The app follows a clean architecture pattern with:

- **Models**: Data models for User, Group, Expense, and Balance entities
- **Services**: Business logic layer for authentication, groups, and expenses
- **Providers**: State management using Provider pattern
- **Screens**: UI layer organized by feature (auth, groups, expenses, profile)

## Getting Started

### Prerequisites

- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Firebase project setup
- Android Studio / VS Code with Flutter extensions

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flutter_try
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication (Email/Password, Google, Facebook)
   - Create a Firestore database
   - Download and configure `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Update `lib/firebase_options.dart` with your Firebase configuration

4. **Run the app**
   ```bash
   flutter run
   ```

### Testing

Run the test suite:
```bash
flutter test
```

Run static analysis:
```bash
flutter analyze
```

## Project Structure

```
lib/
├── models/           # Data models
├── services/         # Business logic and API calls
├── providers/        # State management
├── screens/          # UI screens organized by feature
│   ├── auth/         # Authentication screens
│   ├── home/         # Dashboard and main screens
│   ├── groups/       # Group management screens
│   ├── expenses/     # Expense-related screens
│   └── profile/      # User profile screens
├── firebase_options.dart  # Firebase configuration
├── main.dart         # App entry point
└── app.dart          # Main app widget with routing

test/
├── models/           # Model tests
└── widget_test.dart  # Widget tests
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the repository or contact the development team.
