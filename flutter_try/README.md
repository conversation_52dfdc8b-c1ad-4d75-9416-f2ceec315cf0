# SplitWise - Bill Splitting App

A Flutter app for splitting bills and managing group expenses with Firebase backend.

## ✅ **Recent Updates (Fixed Issues)**

### **1. Navigation Fixed**
- ✅ **Added proper back navigation** throughout the app
- ✅ **Fixed app closing issue** when pressing back button
- ✅ **Implemented nested routing** with Go<PERSON>outer for better navigation flow
- ✅ **Added proper navigation hierarchy**: Dashboard → Groups → Group Details → Add Expense

### **2. Firebase Integration**
- ✅ **Added Firebase Firestore** for data persistence
- ✅ **Implemented Firebase Authentication** for user management
- ✅ **Real-time data synchronization** for groups and expenses
- ✅ **Proper user document creation** in Firestore
- ✅ **Group data now persists** across app sessions

## 🚀 **Features**

### **Authentication**
- Email/password registration and login
- Google Sign-In integration
- Facebook authentication
- Firebase Auth backend with proper error handling

### **Group Management**
- Create groups with member invitations
- Real-time group updates via Firestore streams
- Add/remove members from groups
- Group persistence across sessions

### **Expense Management**
- Add expenses with flexible splitting options
- Equal, percentage, and exact amount splits
- Category-based expense organization
- Real-time expense tracking

### **Navigation**
- Proper back button functionality
- Nested routing structure
- Bottom navigation with Dashboard, Groups, Profile tabs
- Smooth navigation between screens

## 🔧 **Technical Stack**

- **Frontend**: Flutter with Material Design 3
- **Backend**: Firebase (Firestore + Auth)
- **State Management**: Provider pattern
- **Navigation**: GoRouter with nested routes
- **Authentication**: Firebase Auth + Google/Facebook Sign-In

## 🔥 **Firebase Setup Required**

To use this app with real Google Cloud Firestore backend, follow these steps:

### **Step 1: Create Firebase Project**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name (e.g., "splitwise-app")
4. Enable Google Analytics (optional)
5. Click "Create project"

### **Step 2: Enable Authentication**
1. In Firebase Console, go to **Authentication** → **Sign-in method**
2. Enable **Email/Password** authentication
3. Enable **Google** sign-in (add your app's SHA-1 fingerprint)
4. Enable **Facebook** sign-in (add Facebook App ID and secret)

### **Step 3: Create Firestore Database**
1. Go to **Firestore Database** → **Create database**
2. Choose **Start in test mode** (for development)
3. Select a location (choose closest to your users)
4. Click "Done"

### **Step 4: Add Android App**
1. Click **Project Settings** → **Add app** → **Android**
2. Enter package name: `com.example.flutter_try`
3. Download `google-services.json`
4. Place it in `android/app/` directory

### **Step 5: Update Firebase Configuration**
Replace the demo values in `lib/firebase_options.dart` with your actual Firebase config from Project Settings.

### **Step 6: Deploy Firestore Rules**
Use the provided `firestore.rules` file:
```bash
firebase deploy --only firestore:rules
```

### **Current Status**
- ✅ App builds and runs successfully
- ✅ Navigation works properly with back button support
- ✅ Firebase integration ready with Google Cloud Firestore
- ✅ Authentication flow fixed (Google Sign-In now navigates to dashboard)
- ⚠️ **Firebase config needs to be updated** with real project credentials for full functionality

## � **Navigation Flow**

```
Login Screen
    ↓
Dashboard (Bottom Navigation)
├── Dashboard Tab
│   ├── Recent Expenses
│   └── Quick Actions
├── Groups Tab
│   ├── My Groups List
│   ├── Create Group → [Back to Groups]
│   └── Group Details → Add Expense → [Back to Group Details]
└── Profile Tab
    └── User Settings & Sign Out
```

## 🏗️ **Architecture**

The app follows a clean architecture pattern with:

- **Models**: Data models for User, Group, Expense, and Balance entities
- **Services**: Business logic layer for authentication, groups, and expenses
- **Providers**: State management using Provider pattern
- **Screens**: UI layer organized by feature (auth, groups, expenses, profile)

## Getting Started

### Prerequisites

- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Firebase project setup
- Android Studio / VS Code with Flutter extensions

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flutter_try
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication (Email/Password, Google, Facebook)
   - Create a Firestore database
   - Download and configure `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Update `lib/firebase_options.dart` with your Firebase configuration

4. **Run the app**
   ```bash
   flutter run
   ```

## 🎯 **Next Steps**

1. **Set up real Firebase project** with proper credentials
2. **Test group creation and data persistence**
3. **Add expense splitting functionality**
4. **Implement push notifications** for group activities
5. **Add expense categories and filtering**
6. **Implement settlement tracking**

## 🐛 **Issues Fixed**

### **Issue 1: Google Sign-In Navigation ✅ FIXED**
- **Problem**: Google login worked but stayed on same page, didn't navigate to dashboard
- **Solution**:
  - Fixed authentication state listener in AuthProvider
  - Updated router refresh mechanism to respond to auth changes
  - Added proper user document creation/verification for all auth methods
  - Improved loading state management

### **Issue 2: Google Cloud Firestore Integration ✅ FIXED**
- **Problem**: Wanted to use Google Cloud Firestore as database
- **Solution**:
  - Integrated Firebase Firestore with proper collections structure
  - Added optimized queries with `memberIds` array for efficient group lookups
  - Created Firestore security rules for proper access control
  - Updated all data models to work seamlessly with Firestore

### **Other Fixes**
- ✅ **Navigation**: Fixed back button functionality and app closing issues
- ✅ **Data Persistence**: Added Firebase Firestore for real-time data storage
- ✅ **Authentication**: Implemented proper Firebase Auth with social login
- ✅ **Routing**: Fixed nested navigation with GoRouter

### Testing

Run the test suite:
```bash
flutter test
```

Run static analysis:
```bash
flutter analyze
```

## Project Structure

```
lib/
├── models/           # Data models
├── services/         # Business logic and API calls
├── providers/        # State management
├── screens/          # UI screens organized by feature
│   ├── auth/         # Authentication screens
│   ├── home/         # Dashboard and main screens
│   ├── groups/       # Group management screens
│   ├── expenses/     # Expense-related screens
│   └── profile/      # User profile screens
├── firebase_options.dart  # Firebase configuration
├── main.dart         # App entry point
└── app.dart          # Main app widget with routing

test/
├── models/           # Model tests
└── widget_test.dart  # Widget tests
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the repository or contact the development team.
