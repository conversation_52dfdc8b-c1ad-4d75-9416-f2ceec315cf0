rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Groups can be read and written by members
    match /groups/{groupId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.memberIds;
      allow create: if request.auth != null;
    }
    
    // Expenses can be read and written by group members
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
      // TODO: Add more specific rules based on group membership
    }
  }
}
