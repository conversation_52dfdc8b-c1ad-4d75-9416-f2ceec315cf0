# Firebase Setup Guide for SplitWise App

## Quick Setup Checklist

### ✅ **1. Create Firebase Project**
- [ ] Go to [Firebase Console](https://console.firebase.google.com/)
- [ ] Create new project: "splitwise-app" (or your preferred name)
- [ ] Enable Google Analytics (optional)

### ✅ **2. Configure Authentication**
- [ ] Go to Authentication → Sign-in method
- [ ] Enable Email/Password
- [ ] Enable Google (add SHA-1 fingerprint)
- [ ] Enable Facebook (add App ID/Secret)

### ✅ **3. Setup Firestore Database**
- [ ] Go to Firestore Database
- [ ] Create database in test mode
- [ ] Choose location closest to users

### ✅ **4. Add Android App**
- [ ] Project Settings → Add app → Android
- [ ] Package name: `com.example.flutter_try`
- [ ] Download `google-services.json`
- [ ] Place in `android/app/` directory

### ✅ **5. Update Configuration**
- [ ] Copy Firebase config from Project Settings
- [ ] Replace values in `lib/firebase_options.dart`
- [ ] Update project ID, API keys, etc.

### ✅ **6. Deploy Firestore Rules**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize project
firebase init firestore

# Deploy rules
firebase deploy --only firestore:rules
```

## Sample Firebase Config Structure

Your `lib/firebase_options.dart` should look like:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSy...', // Your actual API key
  appId: '1:123456789:android:...', // Your actual app ID
  messagingSenderId: '123456789', // Your sender ID
  projectId: 'your-project-id', // Your project ID
  storageBucket: 'your-project-id.appspot.com',
);
```

## Firestore Collections Structure

The app uses these collections:

- **`users`**: User profiles and settings
- **`groups`**: Bill-splitting groups with members
- **`expenses`**: Individual expenses within groups

## Testing the Setup

1. **Run the app**: `flutter run`
2. **Test Google Sign-In**: Should navigate to dashboard after login
3. **Create a group**: Should persist in Firestore
4. **Check Firestore Console**: Verify data is being saved

## Troubleshooting

### Google Sign-In Issues
- Ensure SHA-1 fingerprint is added to Firebase
- Check package name matches exactly
- Verify `google-services.json` is in correct location

### Firestore Permission Issues
- Check Firestore rules allow authenticated users
- Verify user is properly authenticated
- Check console for specific error messages

### Navigation Issues
- Clear app data and restart
- Check authentication state in debug logs
- Verify router configuration is correct
