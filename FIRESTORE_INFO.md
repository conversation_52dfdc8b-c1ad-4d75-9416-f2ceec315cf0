# Google Cloud Firestore in This App

## What is Google Cloud Firestore?

**Google Cloud Firestore** is Google's NoSQL document database service. It's part of Google Cloud Platform.

## Firebase vs Google Cloud Firestore - What's the Difference?

- **Google Cloud Firestore** = The actual database service
- **Firebase Console** = The web interface to manage Firestore (and other Google Cloud services)
- **Firebase SDK** = The client libraries to connect to Firestore from your app

**Think of it this way:**
- Firestore = The database engine
- Firebase = The management interface and SDK

## Why We Use "Firebase" in Code

The Flutter SDK uses Firebase packages (`cloud_firestore`, `firebase_auth`) because:
1. Firebase provides the easiest client SDKs for mobile apps
2. Firebase Console is the simplest interface to manage Firestore
3. Firebase Auth integrates seamlessly with Firestore security

## What This App Actually Uses

✅ **Database**: Google Cloud Firestore (NoSQL document database)
✅ **Interface**: Firebase Console (to manage the Firestore database)
✅ **Authentication**: Firebase Auth (to secure Firestore access)
✅ **Client SDK**: Firebase Flutter SDK (to connect to Firestore)

## Data Storage Location

Your data is stored in:
- **Google Cloud Firestore** (the actual database)
- **Location**: The region you selected during setup
- **Access**: Through Firebase Console or Google Cloud Console

## Authentication to Firestore

The app authenticates to Google Cloud Firestore using:

1. **User Login**: User signs in via Google/Email
2. **Firebase Auth**: Creates authentication token
3. **Automatic Access**: Token is automatically used for Firestore requests
4. **Security Rules**: Firestore validates access based on the token

**You don't need to manually authenticate to Firestore** - it's handled automatically by the Firebase SDK.

## Collections in Your Firestore Database

- `users/` - User profiles and settings
- `groups/` - Bill-splitting groups with members
- `expenses/` - Individual expenses within groups

## Real-time Features

Google Cloud Firestore provides:
- **Real-time listeners** - Changes appear instantly in the app
- **Offline support** - App works without internet, syncs when online
- **Multi-device sync** - Changes appear on all user devices immediately

## Cost

Google Cloud Firestore pricing:
- **Free tier**: 50,000 reads, 20,000 writes, 20,000 deletes per day
- **Pay-as-you-go**: After free tier limits
- **Perfect for development and small apps**
